from odoo import fields, models, api

class ZnsConfigParam(models.Model):
    _name = 'th.zns.config.param'
    _description = 'ZNS Config Param'
    _rec_name = 'th_param_id'

    # Tên tham số
    th_param_id = fields.Many2one(comodel_name='th.zns.param', string='Tên tham số', store=True, required=True)
    # Bảng
    th_model_id = fields.Many2one(comodel_name='ir.model', string='Bảng', store=True, required=True, ondelete='cascade')
    # Trường
    th_field_id = fields.Many2one(comodel_name='ir.model.fields', string='Trường', store=True, required=True, ondelete='cascade')

