<odoo>
<!--    view form crm-->
    <record id="th_crm_lead_view_form" model="ir.ui.view">
        <field name="name">th_crm_lead_view_form</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.crm_lead_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <field name="th_is_stage_won" invisible="1"/>
            </xpath>
            <xpath expr="(//widget[@name='web_ribbon'])[2]" position="attributes">
                <attribute name="attrs">{'invisible': [('th_is_stage_won', '=', False)]}</attribute>
            </xpath>
            <xpath expr="//label[@for='email_from']" position="attributes">
                <attribute name="attrs">{'invisible': [('partner_id', '=', False)]}</attribute>
            </xpath>
            <xpath expr="//label[@for='phone']" position="attributes">
                <attribute name="attrs">{'invisible': [('partner_id', '=', False)]}</attribute>
            </xpath>
            <xpath expr="//div[@class='o_row o_row_readonly'][2]" position="attributes">
                <attribute name="attrs">{'invisible': [('partner_id', '=', False)]}</attribute>
            </xpath>
            <xpath expr="//div[@class='o_row o_row_readonly'][1]" position="attributes">
                <attribute name="attrs">{'invisible': [('partner_id', '=', False)]}</attribute>
            </xpath>
            <xpath expr="//button[@name='toggle_active']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='name']" position="attributes">
                <attribute name="required">1</attribute>
                <attribute name="readonly">1</attribute>
            </xpath>
            <xpath expr="//button[@name='action_set_won_rainbowman']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='%(crm.crm_lead_lost_action)d']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='action_set_lost']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='action_show_potential_duplicates']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//div[hasclass('oe_title')]//h2" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='action_schedule_meeting']" position="attributes">
                <attribute name="invisible">context.get('is_css_lead', False)</attribute>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_open_profile" type="object" class="oe_stat_button" icon="fa-address-card"
                        context="{'partner_id': partner_id}"
                        invisible="context.get('is_css_lead', False)"
                        attrs="{'invisible': ['|',('th_student_profile_id', '=', False),('th_student_profile_archived','=',True)]}">
                    <div class="o_stat_info">
                        <field name="th_student_profile_id" readonly="1" class="o_stat_value"
                               options="{'no_open': True}"/>
                        <field name="th_profile_status" readonly="1"/>
                    </div>
                </button>
                <button name="action_open_profile_customer" type="object" class="oe_stat_button"
                        invisible="context.get('is_css_lead', False)"
                        attrs="{'invisible': [('th_is_close_lead','=',True)]}"
                        icon="fa-address-card">
                    <div class="o_stat_info">
                        <span class="o_stat_text">
                            Liên hệ
                        </span>
                    </div>
                </button>
                <field name="th_is_a_duplicate_opportunity" invisible="1"/>
                <button name="action_view_history" type="object" class="oe_stat_button"
                        invisible="context.get('is_css_lead', False)"
                        icon="fa-history" attrs="{'invisible': [('th_is_a_duplicate_opportunity', '!=', True)]}">
                    <div class="o_stat_info">
                        <span class="o_stat_text">
                            Lịch sử chăm sóc
                        </span>
                    </div>
                </button>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="after">
                <widget name="web_ribbon" title="Cơ hội trùng" bg_color="bg-danger" attrs="{'invisible': [('th_is_a_duplicate_opportunity', '=', False)]}"/>
                <widget name="web_ribbon" title="Đã bị đóng" bg_color="bg-danger" attrs="{'invisible': [('th_is_close_lead', '=', False)]}"/>
            </xpath>

            <xpath expr="//header" position="inside">
                <field name="th_is_a_duplicate_opportunity" invisible="1"/>
                <field name="th_dup_need_admin" invisible="1"/>
                <field name="th_is_close_lead" invisible="1"/>
                <field name="th_withdraw_profile_id" invisible="1"/>
                <field name="th_invoice_status" invisible="1"/>
                <field name="readonly_domain" invisible="1"/>
                <field name="th_domain_stage_id" invisible="1"/>
                <field name="th_stage_auto" invisible="1"/>
                <field name="stage_id" widget="th_statusbar" class="o_field_statusbar"
                       attrs="{'invisible': ['|',('th_stage_auto', '=', False),('th_is_a_duplicate_opportunity', '=', True)]}"
                       domain="[('th_type', '=', 'crm'), ('id', 'in', th_domain_stage_id)]"/>
                <button name="action_create_profile" string="Tạo hồ sơ"
                        invisible="context.get('is_css_lead', False)"
                        type="object" class="oe_highlight" title="Create profile"
                        attrs="{'invisible': ['|', '|', ('th_student_profile_id', '!=', False), ('th_is_a_duplicate_opportunity', '=', True),('th_is_close_lead', '=', True)]}"/>
                <button name="th_open_dup_lead" string="Danh sách lead trùng" type="object" class="oe_highlight"
                        title="Danh sách lead trùng" invisible="not context.get('manual', False)"/>
                 <button name="th_keep_lead" string="Giữ cơ hội" type="object" class="oe_highlight"
                        title="Giữ cơ hội" invisible="not context.get('manual', False)"/>
                <button name="action_withdraw_profile" string="Rút hồ sơ" type="object" class="oe_highlight"
                        attrs="{'invisible': ['|','|',('th_student_profile_id', '=', False),('th_is_close_lead', '=', True),('th_student_profile_archived','=',True)]}"
                        invisible="context.get('is_css_lead', False)"
                        confirm="Bạn có chắc chắn muốn rút hồ sơ?"/>
<!--                <button name="action_refresh_level" string="Refresh" type="object" class="oe_highlight" groups="base.group_no_one"/>-->
                <button name="action_close_lead" string="Đóng cơ hội" type="object" class="oe_highlight"
                        groups="th_crm.th_group_leader_crm,th_crm.th_group_admin_crm"
                        attrs="{'invisible': ['|', ('th_is_close_lead', '=', True), ('th_is_a_duplicate_opportunity', '=', True)]}"/>
                <button name="action_open_lead" string="Mở cơ hội" type="object" groups="th_crm.th_group_leader_crm" class="oe_highlight" attrs="{'invisible': [('th_is_close_lead', '=', False)]}"/>
                <button name="th_send_noti_duplicate_lead" string="khiếu nại" type="object" class="oe_highlight"
                        invisible="context.get('is_css_lead', False)"
                        attrs="{'invisible': [('th_is_a_duplicate_opportunity', '!=', True)]}"/>
                <button name="th_action_create_new_lead" string="Xác nhận tạo mới" type="object" class="oe_highlight"
                        invisible="not context.get('view_deplicate', False)"
                        attrs="{'invisible': [('th_dup_need_admin', '!=', True)]}"
                        confirm="Xác nhận tạo mới cơ hội này?"
                        groups="th_crm.th_group_admin_crm"/>
                <field name="stage_id" widget="statusbar"
                       domain="[('th_type', '=', 'crm'), ('id', 'in', th_domain_stage_id)]"
                       attrs="{'invisible': [('th_is_a_duplicate_opportunity', '!=', True)]}"/>
<!--                <field name="stage_id" widget="statusbar"-->
<!--                       domain="[('th_type', '=', 'crm'), ('id', 'in', th_domain_stage_id)]"-->
<!--                       attrs="{'invisible': [('th_is_close_lead', '=', False)]}"/>-->

            </xpath>
            <xpath expr="//header/button[@name='action_sale_quotations_new']" position="attributes">
                <attribute name="string">Tạo đơn hàng</attribute>
                <attribute name="attrs">{'invisible': ['|',('th_is_close_lead', '!=', False),
                    ('th_is_a_duplicate_opportunity', '=', True)]}
                </attribute>
                <attribute name="invisible">context.get('is_css_lead', False)</attribute>
            </xpath>
            <xpath expr="//header/field[@name='stage_id']" position="attributes">
                <attribute name="domain">[('th_type', '=', 'crm'), ('id', 'in', th_domain_stage_id)]</attribute>
                <attribute name="widget">th_statusbar</attribute>
                <attribute name="attrs">{'invisible': ['|', '|','|',('th_is_close_lead', '!=', False) ,('th_stage_auto', '!=', False), ('th_is_a_duplicate_opportunity', '=', True), ('id', '=', False)]}
                </attribute>
                <attribute name="invisible">context.get('is_css_lead', False)</attribute>
            </xpath>
            <xpath expr="//group[@name='opportunity_partner']//field[@name='partner_id']" position="after">
                <field name="th_check_crm_phone" attrs="{'invisible': [('partner_id', '!=', False)]}"/>
                <field name="th_check_crm_email" attrs="{'invisible': [('partner_id', '!=', False)]}"/>
            </xpath>

            <xpath expr="//div[hasclass('oe_title')]" position="inside">
                <h2 class="d-flex gap-2 g-0 align-items-end pb-3">
                    <div attrs="{'invisible': [('th_customer_code_gf', '!=', False)]}">
                        <label for="th_customer_code" class="oe_edit_only pb-1"/>
                        <div class="d-flex align-items-end">
                            <field name="th_customer_code" readonly="1"/>
                        </div>
<!--                        <label for="th_customer_code_aum" class="oe_edit_only pb-1"/>-->
<!--                        <div class="d-flex align-items-end">-->
<!--                            <field name="th_customer_code_aum" readonly="1"/>-->
<!--                        </div>-->
                    </div>
                    <div attrs="{'invisible': [('th_customer_code_gf', '=', False)]}">
                        <label for="th_customer_code_gf" class="oe_edit_only pb-1"/>
                        <div class="d-flex align-items-end">
                            <field name="th_customer_code_gf" readonly="1"/>
                        </div>
                    </div>
                    <div>
                        <label for="th_last_check" class="oe_edit_only pb-1"/>
                        <div class="d-flex align-items-end">
                            <field name="th_last_check" widget="remaining_days" readonly="1"/>
                        </div>
                    </div>
                </h2>
            </xpath>
            <xpath expr="//group[@name='opportunity_partner']//field[@name='partner_id']" position="attributes">
                <attribute name="attrs">{'readonly': [('id', '!=', False)]}</attribute>
                <attribute name="options">{'no_quick_create': 1}</attribute>
                <attribute name="context">{'res_partner_search_mode': type == 'opportunity' and 'customer' or False,
                    'default_name': contact_name or partner_name,
                    'default_street': street,
                    'default_street2': street2,
                    'default_city': city,
                    'default_title': title,
                    'default_state_id': state_id,
                    'default_zip': zip,
                    'default_country_id': country_id,
                    'default_function': function,
                    'default_phone': phone,
                    'default_mobile': mobile,
                    'default_email': email_from,
                    'default_user_id': user_id,
                    'default_team_id': team_id,
                    'default_website': website,
                    'default_lang': lang_code,
                    'show_vat': True,
                    'crm_contact': True,
                    'default_phone': th_check_crm_phone,
                    'default_email': th_check_crm_email, }
                </attribute>
                <attribute name="required">1</attribute>
                <attribute name="domain">[('id', 'in', [])]</attribute>
                <attribute name="string">Khách hàng *</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="after">
                <field name="th_domain_user_id" invisible="1"/>
                <field name="th_hide_action_confirm_crm" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="options">{'no_create': 1, 'no_open':1}</attribute>
                <attribute name="string">Người phụ trách</attribute>
                <attribute name="widget"></attribute>
            </xpath>

            <xpath expr="//field[@name='lost_reason_id']" position="before">
                <field name="th_student_profile_archived" invisible="1"/>
                <field name="th_phone2" widget="phone" attrs="{'invisible': [('partner_id', '=', False)]}"/>
                <xpath expr="//group[@name='opportunity_partner']//field[@name='partner_id']" position="move"/>
                <field name="th_check_admission" invisible="1"/>
<!--                <field name="th_student_profile_id"/>-->
                <field name="th_admission_decision" invisible="1"/>
                <field name="th_is_refunded_tuition" invisible="1" readonly="1"/>
                <field name="th_is_close_lead" invisible="1"/>
                <field name="th_admissions_region_id" string="Vùng tuyển sinh*" required="0"
                       options='{"no_open": True, "no_create": True}'
                       attrs="{'required': [('th_required_fill', '=', True)]}"/>
                <field name="th_admissions_station_id" string="Trạm tuyển sinh*" required="0"
                       attrs="{'required': [('th_required_fill', '=', True)]}"
                       options='{"no_open": True, "no_create": True}'/>
                <field name="th_required_fill" invisible="1"/>
                <field name="th_major_ids" widget="many2many_tags" invisible="1"/>
                <field name="th_major_id" options="{'no_create': True, 'no_open': True}" domain="th_domain_major"/>
                <field name="th_domain_major" invisible="1"/>
                <field name="th_graduation_system_id" options='{"no_open": True, "no_create": True}'/>
                <field name="th_training_system_id" options='{"no_open": True, "no_create": True}'/>
            </xpath>
            <xpath expr="//div[hasclass('oe_button_box')]" position="after">
                <widget name="web_ribbon" title="CHờ xét tuyển"
                        attrs="{'invisible': [('th_check_admission', '!=', True)]}"/>
                <widget name="web_ribbon" title="Đã trúng tuyển"
                        attrs="{'invisible': [('th_admission_decision', '!=', True)]}"/>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="before">
                <field name="th_fees" attrs="{'invisible': [('th_fees', '=', False)]}" readonly="1" />
                <field name="th_tuition_handed" attrs="{'invisible': [('th_tuition_handed', '=', False)]}" readonly="1" />
                <field name="th_admission_decision" readonly="1" invisible="1"/>
<!--                <field name="th_type" invisible="1"/>-->
                <field name="th_status_group_id" string="Nhóm tình trạng cơ hội*" options="{'no_create': True, 'no_open': True}" required="1"/>
                <field name="th_status_detail_id" string="Trạng thái chi tiết *" required="1" attrs="{'invisible': [('th_status_group_id', '=', False)]}" domain="[('th_status_category_id', '=', th_status_group_id), ('th_crm_level_ids', 'in', stage_id)]" options="{'no_create': True, 'no_open': True}"/>
                <field name="state" string="Kiểu chăm sóc" attrs="{'invisible': [('state', '=', False)],'readonly': [('th_check_admin_crm', '=', False)]}"/>
                <field name="th_level_up_date" invisible="1"/>
                <field name="th_date_of_payment" invisible="1"/>
                <field name="th_settlement_date" readonly="1" attrs="{'invisible': [('th_settlement_date', '=', False)]}"/>
                <field name="th_data_getfly" invisible="1"/>
            </xpath>

            <xpath expr="//page[@name='lead']//field[@name='website']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='lang_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='zip']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='street2']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='city']" position="before">
                <field name="th_ward_id" placeholder="Xã / Phường" class="o_address_ward" style="width:50%"
                       options="{'no_create': True, 'no_open': True}"/>
                <field name="th_district_id" placeholder="Quận / Huyện" class="o_address_district" style="width:50%"
                       options="{'no_create': True, 'no_open': True}"/>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='city']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='state_id']" position="attributes">
                <attribute name="style">width:100%</attribute>
                <attribute name="options">{'no_create': True,'no_open': True,}</attribute>
            </xpath>

            <xpath expr="//field[@name='mobile']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='contact_name']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='function']" position="before">
                <xpath expr="//page[@name='lead']//field[@name='title']" position="move"/>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='function']" position="after">
                <field name="th_gender"/>
                <label for="th_place_of_birth_id"/>
                <div class="o_row">
                    <field name="th_place_of_birth_id" options='{"no_open": True, "no_create": True}'/>
                    <label for="th_birthday"/>
                    <field name="th_birthday"/>
                </div>
                <label for="th_ethnicity_id"/>
                <div class="o_row">
                    <field name="th_ethnicity_id" options='{"no_open": True, "no_create": True}'/>
                    <label for="th_religion_id"/>
                    <field name="th_religion_id" options='{"no_open": True, "no_create": True}'/>
                </div>
            </xpath>
            <xpath expr="//page[@name='lead']//group[hasclass('mt48')]//label[@for='mobile_page_lead']"
                   position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//group[hasclass('mt48')]//div[hasclass('o_row_readonly')]"
                   position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//group//label[@for='date_deadline']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//group//div[hasclass('o_lead_opportunity_form_inline_fields')]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//group[3]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']//group[@name='Misc']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='internal_notes']" position="attributes">
                <attribute name="string">Mô tả</attribute>
            </xpath>
            <xpath expr="//page[@name='internal_notes']//field[@name='description']" position="replace">
                <field name="th_description"/>
            </xpath>
            <xpath expr="//page[@name='lead']" position="attributes">
                <attribute name="string">Thông tin liên hệ</attribute>
            </xpath>
            <xpath expr="//page[@name='lead']/group/group[1]/field[@name='website']" position="before">
                <label string="Hộ khẩu thường trú" for="th_street"/>
                <div class="o_address_format">
                    <field name="th_street" placeholder="Địa chỉ..." class="o_address_street"/>
                    <field name="th_ward_permanent_id" placeholder="Xã / Phường" class="o_address_ward"
                           style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                    <field name="th_district_permanent_id" placeholder="Quận / Huyện" class="o_address_district"
                           style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                    <field name="th_state_id" class="o_address_state" style="width:100%" placeholder="Tỉnh/ Tp"
                           options="{'no_open': True, 'no_quick_create': True}"
                           context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                    <field name="th_country_id" placeholder="Quốc gia" class="o_address_country"
                           options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}"/>
                </div>
            </xpath>
            <xpath expr="//page[@name='lead']" position="after">
                <page string="Thông tin CSKH">
                    <group>
                        <group>
                            <field name="th_ccs_status_detail_id"
                                   options="{'no_create': True, 'no_open': True}"
                                   readonly="1"/>
                            <field name="th_ccs_customer_attitude_id"
                                   options="{'no_create': True, 'no_open': True}"
                                   readonly="1"/>
                            <field name="th_ccs_reuse_origin_ids"
                                   widget="many2many_tags"
                                   readonly="1"/>
                        </group>
                        <group>
                            <field name="th_ccs_source_code_ccs"
                                   readonly="1"/>
                            <field name="th_ccs_source_code_reused"/>
                        </group>
                    </group>
                </page>
                <page string="Thông tin khác" name="other_infor">
                    <group>
                        <group string="Thông tin khác" name="other_info1">
                            <field name="th_ownership_id" options='{"no_open": True, "no_create": True}' required="1"/>
                            <field name="th_origin_id" options='{"no_open": True, "no_create": True}'
                                   domain="[('th_module_ids.name', 'in', ['CRM'])]" required="1"/>
                            <field name="th_channel_id" options='{"no_open": True, "no_create": True}' attrs="{'required': [('th_is_a_duplicate_opportunity', '!=', True)]}"/>
                            <field name="th_source_group_id" options='{"no_open": True, "no_create": True}' attrs="{'required': [('th_is_a_duplicate_opportunity', '!=', True)]}"/>
                            <field name="th_source_name"/>
                            <field name="th_form_name" readonly="1"/>
                            <field name="th_uuid_form" readonly="1"/>
                        </group>
                        <group string="">
                            <field name="th_crm_job"/>
                            <field name="th_reuse_source"/>
                            <field name="th_reuse"/>
                            <field name="th_check_partner_referred" invisible="1"/>
                            <field name="th_partner_referred_id" context="{'filter': True}"
                                   options='{"no_open": True, "no_create": True}'
                                   attrs="{'readonly': [('th_check_partner_referred', '=', True)]}"/>
                            <field name="th_connector_referred_id" context="{'filter': True}"
                                   options='{"no_open": True, "no_create": True}'
                                   attrs="{'readonly': [('th_connector_referred_id', '=', True)]}"/>
                            <field name="th_affiliate_code" readonly="1"/>
                            <field name="th_check_admin_crm" invisible="1"/>
<!--                            <field name="th_check_tvts_crm" invisible="1"/>-->
                            <field name="th_self_lead" attrs="{'readonly': [('th_check_admin_crm', '=', False)]}"/>
                            <field name="th_confirm_self_lead" attrs="{'invisible': [('th_self_lead', '=', False)]}"/>
                        </group>
                    </group>
                </page>
                <page string="Cơ hội">
                    <field name="th_opportunity_list_partner_crm_ids">
                        <tree no_open="1">
                            <field name="name"/>
                            <field name="th_partner_id"/>
                            <field name="th_origin_id"/>
                            <field name="th_last_check"/>
                            <field name="th_stage_id"/>
                        </tree>
                        <form read="0">

                        </form>
                    </field>
                </page>
                 <page string="Thông tin giới thiệu" attrs="{'invisible': [('th_utm_source', '=', False),('th_utm_medium', '=', False),('th_utm_campaign', '=', False),('th_utm_term', '=', False),('th_utm_content', '=', False)]}">
                    <group>
                        <field name="th_utm_source" readonly="1"/>
                        <field name="th_utm_medium" readonly="1"/>
                        <field name="th_utm_campaign" readonly="1"/>
                        <field name="th_utm_term" readonly="1"/>
                        <field name="th_utm_content" readonly="1"/>
                    </group>
                </page>
                <page string="Mô tả check trùng" attrs="{'invisible': [('th_duplicate_description', '=', False)]}">
                    <field name="th_duplicate_description" readonly="1"/>
                </page>
                <page string="Data Getfly" name="data_getfly" attrs="{'invisible': [('th_data_getfly', '=', False)]}">
                    <field name="th_data_getfly" readonly="1"/>
                </page>
                <page string="Dữ liệu Getfly">
                    <group>
                        <group>
                            <field name="th_create_lead_date_getfly"/>
                            <field name="th_level_up_date_getfly"/>
                            <field name="th_l5b_aof_date_getfly"/>
                        </group>
                        <group>
                            <field name="th_reuse_ccs_getfly"/>
                            <field name="th_name_ccs_getfly"/>
                            <field name="th_code_ccs_getfly"/>
                        </group>
                    </group>
                </page>
            </xpath>
            <xpath expr="//field[@name='lead_properties']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='tag_ids']" position="attributes">
                <attribute name="invisible">0</attribute>
            </xpath>
            <xpath expr="//field[@name='tag_ids']" position="after">
                <field name="th_date_lead_again" attrs="{'invisible': [('th_date_lead_again', '=', False)], 'readonly': [('th_date_lead_again', '!=', False)]}"/>
            </xpath>
            <xpath expr="//field[@name='tag_ids']" position="before">
                <field name="th_student_code"
                       attrs="{'invisible': [('th_admission_decision', '!=', True)], 'readonly': [('th_admission_decision', '=', True)]}"/>
                <field name="th_class"
                       attrs="{'invisible': [('th_admission_decision', '!=', True)], 'readonly': [('th_admission_decision', '=', True)]}"/>
                <field name="th_class_detail"
                       attrs="{'invisible': [('th_admission_decision', '!=', True)], 'readonly': [('th_admission_decision', '=', True)]}"/>
            </xpath>
            <xpath expr="//page[@name='lead']//field[@name='partner_name']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="before">
                <field name="team_id" options="{'no_create': 1, 'no_open':1}" string="Đội tư vấn" invisible="1"/>
                <field name="th_crm_lead_b2b_id" groups="base.group_no_one" readonly="1"/>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="before">
                <field name="th_dividing_ring_id" options="{'no_create': 1, 'no_open':1}" domain="[('th_is_crm_lead_dividing', '=', True),('th_origin_id', '=?', th_origin_id)]"/>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="after">
                <field name="th_registration_date" readonly="1"/>
            </xpath>
        </field>
    </record>

<!--    replace button tạo sale order sang invoice-->
    <record id="th_crm_case_form_view_oppor_sale_crm" model="ir.ui.view">
        <field name="name">th_crm_case_form_view_oppor</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="sale_crm.crm_case_form_view_oppor"/>
        <field name="arch" type="xml">

            <!--            <xpath expr="//button[@name='action_sale_quotations_new']" position="replace">-->
            <!--                <button name="th_action_invoice_new" string="Tạo đơn hàng" type="object" class="oe_highlight"-->
            <!--                attrs="{'invisible': [('th_count_invoice', '>', 0)]}"/>-->
            <!--            </xpath>-->
            <xpath expr="//button[@name='action_view_sale_quotation']" position="attributes">
                <attribute name="invisible">1</attribute>
                <!--                <button class="oe_stat_button" type="object" name="action_view_invoice" icon="fa-pencil-square-o"-->
                <!--                attrs="{'invisible': [('th_count_invoice', '=', 0)]}">-->
                <!--                    <field name="th_count_invoice" widget="statinfo" string="Đơn hàng"/>-->
                <!--                </button>-->
            </xpath>

            <xpath expr="//button[@name='action_view_sale_order']" position="replace">
                <button class="oe_stat_button" type="object"
                        attrs="{'invisible': ['|', ('sale_order_count', '=', 0), ('type', '=', 'lead')]}"
                        name="action_view_sale_order" icon="fa-shopping-cart">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <!--                                <field name="sale_amount_total" widget="monetary" options="{'currency_field': 'company_currency'}"/>-->
                            <field name="sale_order_count"/>
                        </span>
                        <span class="o_stat_text">Đơn hàng</span>
                        <field name="sale_order_count" invisible="1"/>
                    </div>
                </button>
            </xpath>

        </field>
    </record>

<!--    view form crm chờ xác nhận-->
    <record id="th_crm_lead_handed_wait_for_confirmation_view_form" model="ir.ui.view">
        <field name="name">th_crm_lead_handed_wait_for_confirmation_view_form</field>
        <field name="model">crm.lead</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                        <field name="th_fees"/>
                        <field name="th_tuition_handed" widget="radio" options="{'horizontal':true}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

<!--    view tree crm-->
    <record id="th_crm_case_tree_view_oppor" model="ir.ui.view">
        <field name="name">th_crm_case_tree_view_oppor</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.crm_case_tree_view_oppor"/>
        <field name="arch" type="xml">

            <xpath expr="//tree" position="attributes">
                <attribute name="js_class">crm_lead_list</attribute>
                <attribute name="multi_edit">1</attribute>
                <attribute name="editable"></attribute>
                <attribute name="default_order">th_last_check desc</attribute>
            </xpath>
            <xpath expr="//field[@name='name']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='name']" position="before">
                <field name="th_duplicate_type" invisible="1"/>
                <field name="th_customer_code" widget="tree_url" readonly="1" />
                <field name="th_customer_code_aum" widget="tree_url" readonly="1" invisible="1"/>
                <field name="th_customer_code_gf" invisible="1" widget="tree_url" readonly="1"/>
                <field name="readonly_domain" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='contact_name']" position="replace">
                <field name="partner_id" position="move"/>
                <field name="th_last_check" widget="remaining_days" readonly="1"/>
                <field name="stage_id" position="move"/>
                <field name="phone" position="move" widget="phone"/>
<!--                <field name="th_phone2" widget="phone" options="{'enable_call': true}"/>-->
                <field name="th_description" string="Mô tả"/>
                <field name="th_status_detail_id"
                       domain="[('th_status_category_id', '=', th_status_group_id), ('th_crm_level_ids', 'in', stage_id)]"
                       options="{'no_create': True}"/>
            </xpath>
            <xpath expr="//field[@name='email_from']" position="after">
                <field name="th_ownership_id" optional="hide" options="{'no_create': True}"/>
                <field name="th_origin_id" optional="hide" options="{'no_create': True}"/>
                <field name="th_admissions_region_id" string="Vùng tuyển sinh *" optional="hide"
                       options="{'no_create': True}" required="1"/>
                <field name="th_admissions_station_id" string="Trạm tuyển sinh *" required="1" optional="hide"
                       options="{'no_create': True}"/>
                <field name="th_channel_id" optional="hide" options="{'no_create': True}"/>
                <field name="state" optional="hide" options="{'no_create': True}"/>
                <field name="th_source_group_id" optional="hide" options="{'no_create': True}"/>
                <field name="th_source_name" optional="hide"/>
                <field name="th_graduation_system_id" optional="hide"/>
                <field name="th_date_of_delivery" optional="hide"/>
                <field name="th_decision_date" optional="hide"/>
                <field name="th_profile_status" optional="hide"/>
                <field name="th_major_ids" optional="hide"/>
                <field name="th_major_id" optional="hide"/>
                <field name="th_registration_date" optional="hide"/>
                <field name="th_status_group_id" optional="hide"/>
                <field name="th_create_lead_date_getfly" optional="hide"/>
                <field name="th_level_up_date_getfly" optional="hide"/>
                <field name="th_l5b_aof_date_getfly" optional="hide"/>
                <field name="th_reuse_ccs_getfly" optional="hide"/>
                <field name="th_name_ccs_getfly" optional="hide"/>
                <field name="th_code_ccs_getfly" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='state_id']" position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
                <attribute name="string">Tỉnh/TP</attribute>
            </xpath>
            <xpath expr="//field[@name='country_id']" position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
                <attribute name="string">Người phụ trách</attribute>
                <attribute name="widget"></attribute>
            </xpath>
            <xpath expr="//field[@name='team_id']" position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='tag_ids']" position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="options">{'no_create_edit': 1}</attribute>
                <attribute name="optional">show</attribute>
                <attribute name="required">1</attribute>
                <attribute name="string">Khách hàng *</attribute>
            </xpath>
            <xpath expr="//field[@name='activity_ids']" position="attributes">
                <attribute name="invisible">0</attribute>
                <attribute name="string">Lịch làm việc</attribute>
            </xpath>
            <xpath expr="//field[@name='my_activity_date_deadline']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='expected_revenue']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='priority']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='activity_user_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='campaign_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='medium_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='source_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='probability']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='city']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='date_deadline']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='%(crm.action_lead_mail_compose)d']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='%(crm.action_lead_mass_mail)d']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='action_snooze']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
<!--            <xpath expr="//button[@name='%(crm.action_lead_mail_compose)d']" position="after">-->
<!--                <field name="th_student_profile_id" invisible="1"/>-->
<!--                <button name="open_crm_form"  type="object" string="Mở form" icon="fa-external-link" />-->
<!--&lt;!&ndash;                <button name="th_create_activities" type="object" icon="fa-clock-o" string="Các hoạt động"/>&ndash;&gt;-->
<!--            </xpath>-->

        </field>
    </record>

    <record id="th_crm_team_view_tree" model="ir.ui.view">
        <field name="name">crm.team.tree.inherit.crm</field>
        <field name="model">crm.team</field>
        <field name="inherit_id" ref="sales_team.crm_team_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="widget"></attribute>
            </xpath>
        </field>
    </record>

<!--    crm_sms-->
    <record id="th_crm_sms_case_tree_view_oppor" model="ir.ui.view">
        <field name="name">th_crm_sms_case_tree_view_oppor</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm_sms.crm_case_tree_view_oppor"/>
        <field name="arch" type="xml">

            <xpath expr="//button[@name='%(crm_sms.crm_lead_act_window_sms_composer_multi)d']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='%(crm_sms.crm_lead_act_window_sms_composer_single)d']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

        </field>
    </record>

    <record id="th_crm_lead_tree_view_admission_decision" model="ir.ui.view">
        <field name="name">th_crm_lead_tree_view_admission_decision</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="th_crm.th_crm_case_tree_view_oppor"/>
        <field name="mode">primary</field>
        <field name="priority">999</field>
        <field name="arch" type="xml">

            <xpath expr="//tree" position="attributes">
                <attribute name="js_class"></attribute>
                <attribute name="create">0</attribute>
            </xpath>
            <xpath expr="//tree" position="inside">
                <header>
                    <button class="btn-primary" name="change_admission" type="object" string="Chuyển sang chờ xét"
                            invisible="not context.get('admission_id')"/>
                </header>
            </xpath>
            <xpath expr="//field[@name='th_customer_code_gf']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='th_last_check']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='stage_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='th_description']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='th_status_detail_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='activity_ids']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="th_gender"/>
                <field name="th_birthday"/>
                <field name="th_place_of_birth_id"/>
            </xpath>
<!--            <xpath expr="//button[@name='open_crm_form']" position="attributes">-->
<!--                <attribute name="invisible">1</attribute>-->
<!--            </xpath>-->
        </field>
    </record>

<!--    view seach crm lead-->
    <record id="th_view_crm_case_opportunities_filter_crm" model="ir.ui.view">
        <field name="name">th_view_crm_case_opportunities_filter_crm</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.view_crm_case_opportunities_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='creation_date']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='close_date']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='stage']" position="attributes">
                <attribute name="string">Mối quan hệ</attribute>
            </xpath>
            <xpath expr="//filter[@name='salesperson']" position="attributes">
                <attribute name="string">Người phụ trách</attribute>
            </xpath>
            <xpath expr="//filter[@name='saleschannel']" position="attributes">
                <attribute name="string">Đội tư vấn</attribute>
            </xpath>
<!--            <xpath expr="//filter[@name='stage']" position="after">
                <filter name="th_last_check" string="Liên hệ lần cuối" context="{'group_by':'th_last_check:day'}"/>
            </xpath>-->
            <xpath expr="//filter[@name='company']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='compaign']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='medium']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='source']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='date_closed']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='date_deadline']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='open_opportunities']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='message_needaction']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='won']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='lost']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='lost']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='lostreason']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="string">Khách hàng</attribute>
                <attribute name="filter_domain">[('partner_id.name', 'ilike', self)]</attribute>
            </xpath>
            <xpath expr="//field[@name='team_id']" position="attributes">
                <attribute name="string">Đội chăm sóc</attribute>
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='country_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='stage_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="string">Người chăm sóc</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='name']" position="attributes">
                <attribute name="filter_domain">[('name', 'ilike', self)]</attribute>
            </xpath>
            <xpath expr="//field[@name='city']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='tag_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='phone_mobile_search']" position="replace">
                <field name="th_customer_code" />
                <field name="th_customer_code_aum" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='name']" position="before">
                <field name="phone_mobile_search" filter_domain="['|',('phone', 'ilike', self),('th_phone2', 'ilike', self)]"/>
            </xpath>
            <xpath expr="//field[@name='tag_ids']" position="after">
                <field name="th_origin_id"/>
                <field name="th_admissions_region_id"/>
                <field name="th_admissions_station_id"/>
                <field name="th_channel_id"/>
                <field name="email_from"/>
                <field name="th_dividing_ring_id" string="Đội chăm sóc"/>
                <field name="th_connector_referred_id" string="Người kết nối"/>
<!--                <field name="th_last_check"/>-->
            </xpath>
            <xpath expr="//search" position="inside">
                    <filter name="th_filter_th_can_be_lead" string="Cơ hội có nhu cầu"
                        domain="[('th_can_be_lead', '=', True),('th_date_lead_again', '!=', False)]"/>
<!--                    <filter name="last_check_1" string="Liên hệ cuối từ 0-> 3 ngày"-->
<!--                             domain="[('th_last_check', '&gt;=', (context_today() - datetime.timedelta(days=3)))]"/>-->
<!--                    <filter name="last_check_5" string="Liên hệ cuối từ 0->7 ngày"-->
<!--                            domain="[('th_last_check', '&gt;=', (context_today() - datetime.timedelta(days=7)))]"/>-->
<!--                    <filter name="last_check_15" string="Liên hệ cuối từ 0-> 15 ngày "-->
<!--                            domain="[('th_last_check', '&gt;=', (context_today() - datetime.timedelta(days=15)))]"/>-->
<!--                    <filter name="last_check_30" string="Liên hệ cuối từ 0-> 30 ngày "-->
<!--                            domain="[('th_last_check', '&gt;=', (context_today() - datetime.timedelta(days=30)))]"/>-->
<!--                    <filter name="last_check_55" string="Liên hệ cuối từ 0-> 55 ngày "-->
<!--                            domain="[('th_last_check', '&gt;=', (context_today() - datetime.timedelta(days=55)))]"/>-->
                    <filter name="last_check_60" string="Liên hệ cuối từ 0-> 61 ngày"
                            domain="[('th_last_check', '&gt;=', (context_today() - datetime.timedelta(days=61)))]"/>
                    <separator/>
                    <filter name="last_check_after_3" string="Liên hệ cuối từ 3 ngày trở lên"
                             domain="[('th_last_check', '&lt;=', (context_today() - datetime.timedelta(days=3)))]"/>
                    <filter name="last_check_after_5" string="Liên hệ cuối từ 5 ngày trở lên"
                            domain="[('th_last_check', '&lt;=', (context_today() - datetime.timedelta(days=5)))]"/>
                    <filter name="last_check_after_7" string="Liên hệ cuối từ 7 ngày trở lên"
                            domain="[('th_last_check', '&lt;=', (context_today() - datetime.timedelta(days=7)))]"/>
                    <filter name="last_check_after_12" string="Liên hệ cuối từ 12 ngày trở lên"
                            domain="[('th_last_check', '&lt;=', (context_today() - datetime.timedelta(days=12)))]"/>
                    <filter name="last_check_after_40" string="Liên hệ cuối từ 40 ngày trở lên"
                            domain="[('th_last_check', '&lt;=', (context_today() - datetime.timedelta(days=40)))]"/>
                    <filter name="last_check_after_55" string="Liên hệ cuối từ 55 ngày trở lên"
                            domain="[('th_last_check', '&lt;=', (context_today() - datetime.timedelta(days=55)))]"/>
                    <separator/>
                    <filter string="Ngày đăng ký" name="date" date="th_registration_date"/>

                    <group expand="0">
                        <filter string="Đội chăm sóc" name="th_dividing_ring_id" context="{'group_by':'th_dividing_ring_id'}"/>
                    </group>
                    <group expand="0">
                        <filter string="Người kết nối" name="th_connector_referred_id" context="{'group_by':'th_connector_referred_id'}"/>
                    </group>
                <searchpanel>
                    <field name="user_id" icon="fa-user-circle" enable_counters="1"/>
                    <field name="stage_id" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_status_detail_id" icon="fa-phone" enable_counters="1"/>
                </searchpanel>
            </xpath>
        </field>
    </record>

<!--    view lead khi sử dụng button list view đưa về kho và tái sử dụng-->
    <record id="th_crm_case_tree_view_oppor_reuse" model="ir.ui.view">
        <field name="name">th_crm_case_tree_view_oppor</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.crm_case_tree_view_oppor"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">

            <xpath expr="//tree" position="attributes">
                <attribute name="js_class"></attribute>
            </xpath>
            <xpath expr="//header" position="replace">
                <header>
                    <button name="th_action_archive" type="object" string="Lưu trữ"
                            invisible="not context.get('storage')"/>
                    <button name="action_create_reuse" type="object" string="Tái sử dụng"
                            invisible="context.get('storage')"/>
                </header>
            </xpath>
        </field>
    </record>

<!--    Duplicate opportunity search view-->
    <record id="th_crm_lead_duplicate_search" model="ir.ui.view">
        <field name="name">th_crm_lead_duplicate_search</field>
        <field name="model">crm.lead</field>
        <field name="arch" type="xml">
            <search string="">
                <group string="Group By">
                    <filter string="Partner" name="group_by_partner" context="{'group_by': 'partner_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!--    view crm duplicate lead list-->
    <record id="th_crm_lead_duplicate_tree" model="ir.ui.view">
        <field name="name">th_crm_lead_duplicate_tree</field>
        <field name="model">crm.lead</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="th_crm_lead_tree_view_admission_decision"/>
        <field name="arch" type="xml">

            <xpath expr="//tree" position="inside">
                <field name="th_key_duplicate" invisible="0"/>
                <field name="th_result" invisible="0"/>
                <!--                <button name="th_action_lead_duplicate_keep" attrs="{'invisible': ['|', ('th_key_duplicate', '!=', True), ('th_result', '=', 'transfer')]}" type="object" string="Giữ" class="btn-primary"/>-->
                <!--                <button name="th_action_lead_duplicate_transfer" attrs="{'invisible': ['|', ('th_key_duplicate', '!=', True), ('th_result', '=', 'keep')]}" type="object" string="Chuyển" class="btn-primary"/>-->
            </xpath>
            <xpath expr="//tree" position="attributes">
                <attribute name="delete">1</attribute>
            </xpath>
        </field>
    </record>

    <!--    view crm duplicate lead form-->
    <record id="th_crm_lead_duplicate_form" model="ir.ui.view">
        <field name="name">th_crm_lead_duplicate_form</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="th_crm.th_crm_lead_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">

            <xpath expr="//header" position="replace">
                <header>
                    <field name="th_key_duplicate" invisible="1"/>
                    <field name="th_result" invisible="1"/>
                    <field name="stage_id" invisible="1"/>
                    <!--                    <button name="th_action_lead_duplicate_keep" attrs="{'invisible': ['|', ('th_key_duplicate', '!=', True), ('th_result', '=', 'transfer')]}" type="object" string="Giữ" class="btn-primary"/>-->
                    <!--                    <button name="th_action_lead_duplicate_transfer" attrs="{'invisible': ['|', ('th_key_duplicate', '!=', True), ('th_result', '!=', 'keep')]}" type="object" string="Chuyển" class="btn-primary"/>-->
                </header>
            </xpath>

        </field>
    </record>

    <record id="th_view_crm_lead_handel_duplicate" model="ir.ui.view">
        <field name="name">th_view_crm_lead_handel_duplicate</field>
        <field name="model">crm.lead</field>
        <field name="arch" type="xml">
            <tree string="" default_order="th_duplicate_date desc"
                  decoration-warning="th_duplicate_type == 'need_handle'"
                  decoration-danger="th_duplicate_type == 'no_results'"
                  decoration-primary="th_duplicate_type == 'auto'"
                  decoration-muted="th_duplicate_type == 'manual'" sample="1">
                <field name="th_customer_code"/>
                <field name="th_customer_code_aum" invisible="1"/>
                <field name="th_customer_code_gf"  invisible="1" readonly="1"/>
                <field name="partner_id"/>
                <field name="th_last_check"/>
                <field name="stage_id"/>
                <field name="th_status_detail_id"/>
                <field name="th_ownership_id"/>
                <field name="th_origin_id" optional="hide"/>
                <field name="th_channel_id"/>
                <field name="th_source_group_id"/>
                <field name="th_admissions_region_id" optional="hide"/>
                <field name="th_admissions_station_id" optional="hide"/>
                <field name="phone" optional="hide"/>
                <field name="email_from" optional="hide"/>
                <field name="th_duplicate_type" invisible="1"/>
            </tree>
        </field>
    </record>


        <record model="ir.actions.act_window" id="crm.crm_lead_action_pipeline">
            <field name="view_mode">tree,form</field>
            <field name="context">{
                'default_type': 'opportunity',
                'search_default_assigned_to_me': 0,
                'create': 0,
                }
            </field>
            <field name="domain">[('type','=','opportunity'), ('th_storage', '=',
                False),('th_is_a_duplicate_opportunity',
                '=', False)]
            </field>
            <field name="view_ids" eval="[(5, 0, 0)]"/>
        </record>


    <record id="th_crm_lead_action_pipeline" model="ir.actions.act_window">
        <field name="name">Cơ hội của tôi</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('type','=','opportunity'), ('th_origin_id', '=', active_id), ('th_storage', '=', False),
            ('th_is_a_duplicate_opportunity', '=', False)]
        </field>
        <field name="context">{
            'create':1,
            'default_type': 'opportunity',
            'default_th_origin_id': active_id,
            }
        </field>
        <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
    </record>
    <record id="th_crm_lead_action_admission" model="ir.actions.act_window">
        <field name="name">Cơ hội</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('type','=','opportunity'), ('th_origin_id', '=', active_id), ('th_storage', '=',
            False)]
        </field>
        <field name="context">{
            'create':1,
            'default_type': 'opportunity',
            'default_th_origin_id': active_id,
            }
        </field>
    </record>

    <record id="th_crm_duplicate_lead" model="ir.actions.act_window">
        <field name="name">Cơ hội trùng</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('type','=','opportunity'),('th_is_a_duplicate_opportunity', '=', True), ('th_storage',
            '=', False)]
        </field>
        <field name="context">{'create':0, 'edit':0, 'delete':0, 'view_deplicate': True}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                <!-- Add Text Here -->
            </p>
            <p>
                <!-- More details about what a user can do with this object will be OK -->
            </p>
        </field>
    </record>
    <!--    <record id="th_crm_merge_history" model="ir.actions.act_window">-->
    <!--        <field name="name">Cơ hội hợp nhất</field>-->
    <!--        <field name="type">ir.actions.act_window</field>-->
    <!--        <field name="res_model">crm.lead</field>-->
    <!--        <field name="view_mode">tree,form</field>-->
    <!--        <field name="domain">[('type','=','opportunity'), ('th_resolve_duplicate', '=', True), ('th_storage', '=', False)]</field>-->
    <!--        <field name="context">{-->
    <!--                'create':0,-->
    <!--        }</field>-->
    <!--    </record>-->


    <record id="th_create_activities_act_server" model="ir.actions.server">
        <field name="name">Lịch làm việc</field>
        <field name="model_id" ref="crm.model_crm_lead"/>
        <field name="binding_model_id" ref="crm.model_crm_lead"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">action = records.th_create_activities()</field>
    </record>

    <record id="th_action_assign_user1" model="ir.actions.act_window">
        <field name="name">Chia cơ hội</field>
        <field name="res_model">crm.lead.reuse</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context" eval="{
                'default_th_create_reuse': True,
                'default_th_origin_id': False,
                'action_assign': True,
                'is_crm': True,
            }"/>
        <field name="view_ids" eval="[(5, 0, 0),
                    (0, 0, {'view_mode': 'form', 'view_id': ref('th_lead_reuse_view_form2')})]"/>
        <field name="binding_model_id" ref="model_crm_lead"/>
        <field name="binding_view_types">list</field>
    </record>

    <record id="crm.action_merge_opportunities" model="ir.actions.act_window">
        <field name="groups_id" eval="[(6, 0, [ref('base.group_no_one')])]"/>
    </record>
    <record id="crm.action_lead_mail_compose" model="ir.actions.act_window">
        <field name="groups_id" eval="[(6, 0, [ref('base.group_no_one')])]"/>
    </record>
    <record id="crm.action_lead_mass_mail" model="ir.actions.act_window">
        <field name="groups_id" eval="[(6, 0, [ref('base.group_no_one')])]"/>
    </record>
    <record id="crm.action_crm_send_mass_convert" model="ir.actions.act_window">
        <field name="groups_id" eval="[(6, 0, [ref('base.group_no_one')])]"/>
    </record>
    <record id="crm_sms.crm_lead_act_window_sms_composer_single" model="ir.actions.act_window">
        <field name="groups_id" eval="[(6, 0, [ref('base.group_no_one')])]"/>
    </record>
    <record id="crm.action_mark_as_lost" model="ir.actions.server">
        <field name="groups_id" eval="[(6, 0, [ref('base.group_no_one')])]"/>
    </record>

    <record id="th_duplicate_crm_search_view" model="ir.ui.view">
        <field name="name">th_duplicate_crm_search_view</field>
        <field name="model">crm.lead</field>
        <field name="arch" type="xml">
            <search string="">
                <field name="name" filter_domain="[('name', 'ilike', self)]"/>
                <filter string="Cần xử lý" name="lead_deduplication_handle" domain="[('th_duplicate_type', '=', 'need_handle')]"/>
                <filter string="Xử lý tự động" name="lead_deduplication_auto" domain="[('th_duplicate_type', '=', 'auto')]"/>
                <filter string="Xử lý thủ công" name="lead_deduplication_manual" domain="[('th_duplicate_type', '=', 'manual')]"/>
                <filter string="Chưa có điều kiện" name="lead_deduplication_no_results" domain="[('th_duplicate_type', '=', 'no_results')]"/>
            </search>
        </field>
    </record>

    <record id="th_action_duplicate_crm" model="ir.actions.act_window">
        <field name="name">Trùng cơ hội</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('type','=','opportunity'), ('th_storage', '=', False), ('th_is_a_duplicate_opportunity', '=', False)]</field>
        <field name="context">{ 'view_duplicate': True, 'search_default_lead_deduplication_handle': 1, 'manual': 'manual', 'create':0, 'delete':0}</field>
        <field name="view_id" ref="th_view_crm_lead_handel_duplicate"/>
        <field name="search_view_id" ref="th_crm.th_duplicate_crm_search_view"/>
    </record>

<!--    <record id="th_action_open_crm_lead" model="ir.actions.act_window">-->
<!--        <field name="name">Cơ hội CRM</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="res_model">crm.lead</field>-->
<!--        <field name="view_mode">form</field>-->
<!--        <field name="domain">[('type','=','opportunity'), ('th_storage', '=', False), ('th_is_a_duplicate_opportunity', '=', False)]</field>-->
<!--        <field name="context">{'create': False, 'edit': False, 'delete': False, 'copy': False, 'is_css_lead': True}</field>-->
<!--        <field name="view_id" ref="th_crm_lead_view_form"/>-->
<!--    </record>-->

</odoo>
