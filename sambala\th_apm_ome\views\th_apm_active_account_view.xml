<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="th_apm_ome_active_account_tree_view" model="ir.ui.view">
        <field name="name">th_apm_active_account_tree_view</field>
        <field name="model">th.apm.active.account</field>
        <field name="arch" type="xml">
            <tree create="0" delete="0">
                <field name="th_customer_name"/>
                <field name="th_customer_code"/>
                <field name="th_customer_phone"/>
                <field name="th_customer_email"/>
                <field name="th_stage"/>
            </tree>
        </field>
    </record>

    <record id="th_apm_ome_active_account_form_view" model="ir.ui.view">
        <field name="name">th_apm_active_account_form_view</field>
        <field name="model">th.apm.active.account</field>
        <field name="arch" type="xml">
            <form create="0" delete="0">
                <header>
                    <field name="th_stage" widget="statusbar" class="o_field_statusbar"/>
                    <button name="re_active" type="object" string="Kích hoạt lại" data-hotkey="z" groups="th_apm.group_apm_administrator"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="th_customer_name" readonly="1"/>
                            <field name="th_customer_code" readonly="1"/>
                            <field name="th_origin_id" readonly="1" options="{'no_create': True, 'no_open': True}"/>
                        </group>
                        <group>
                            <field name="th_customer_phone" readonly="1"/>
                            <field name="th_customer_email" readonly="1"/>
                            <field name="th_sale_order_id" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Sản phẩm">
                            <field name="th_apm_active_account_line_ids">
                                <tree editable="bottom" create="0" delete="0">
                                    <field name="th_apm_active_account_id" invisible="1"/>
                                    <field name="th_product_id" readonly="1" options="{'no_open': True}"/>
                                    <field name="th_course_activation_status" readonly="1"/>
                                    <field name="th_waiting_reason" readonly="1"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Mô tả">
                            <field name="th_description" placeholder="Thêm mô tả..."/>
                        </page>
                        <page string="Log">
                            <field name="th_log_api_ids">
                                <tree>
                                    <field name="th_function_call" optional="show"/>
                                    <field name="create_date" optional="show"/>
                                    <field name="th_time_response" optional="show"/>
                                    <field name="state" optional="show"/>
                                    <field name="th_model" optional="hide"/>
                                    <field name="th_record_id" optional="hide"/>
                                    <field name="th_input_data" optional="hide"/>
                                    <field name="th_description" optional="hide"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="th_apm_ome_active_account_search_view" model="ir.ui.view">
        <field name="name">th_apm_active_account_search_view</field>
        <field name="model">th.apm.active.account</field>
        <field name="arch" type="xml">
            <search>
                <field name="th_customer_name"/>
                <field name="th_customer_code"/>
                <field name="th_customer_phone"/>
                <field name="th_customer_email"/>
                <searchpanel>
                    <field name="th_stages" string="Trạng thái kích hoạt tài khoản" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_course_activation_status" string="Trạng thái kích hoạt khóa học" icon="fa-check" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <!-- Action cho Trạng thái kích hoạt tài khoản -->
    <record id="th_apm_ome_active_account_action_server" model="ir.actions.server">
        <field name="name">Trạng thái kích hoạt tài khoản</field>
        <field name="model_id" ref="th_apm.model_th_apm_active_account"/>
        <field name="state">code</field>
        <field name="code">action = model.action_view_apm_ome_active_account()</field>
    </record>
</odoo>
