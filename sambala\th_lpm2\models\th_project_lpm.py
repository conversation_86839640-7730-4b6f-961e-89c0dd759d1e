from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
from odoo.tools.safe_eval import safe_eval
import json

class ThProjectLpm(models.Model):
    _name = 'th.project.lpm2'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = 'Dự án sản xuất học liệu LPM2'

    name = fields.Char(string='Tên dự án')
    th_project_code = fields.Char(string='Mã dự án')
    th_description = fields.Text(string='Diễn giải')
    th_course_count = fields.Integer(string='Số lượng khóa học', compute='_compute_course_count')
    th_project_manager_id = fields.Many2one('res.users', string='Quản lý dự án')
    th_member_ids = fields.Many2many('res.users', string='Nhân sự tham gia')
    th_start_date = fields.Date(string="<PERSON><PERSON><PERSON> bắt đầu")
    th_end_date = fields.Date(string="<PERSON><PERSON><PERSON> kết thúc")
    th_department = fields.Char(string='Bộ phận')
    th_production_office_id = fields.Many2one('th.office.production', string='Phòng sản xuất')
    th_project_status_id = fields.Many2one('th.project.status', string='Trạng thái dự án',
                                           group_expand='_read_group_request_status')
    th_course_ids = fields.One2many('th.course.lpm2', 'th_project_id', string='Danh sách khóa học')
    th_origin_id = fields.Many2one('th.origin', string='Trường')

    # các trường này sẽ được sử dụng trong dự án dài hạn
    th_customer_object = fields.Char(string='Đối tượng khách hàng')
    th_major_ids = fields.Many2many('th.major', string='Ngành học')
    th_project_manager_id_domain = fields.Char(compute='_compute_project_manager_id_domain', string='Domain quản lý dự án')

    # domain lấy quản lý dự án theo phòng sản xuất
    @api.depends('th_production_office_id')
    def _compute_project_manager_id_domain(self):
        for record in self:
            domain = []
            if record.th_production_office_id:
                user_ids = record.th_production_office_id.th_user_ids.ids
                if user_ids:
                    domain = [('id', 'in', user_ids)]

            record.th_project_manager_id_domain = json.dumps(domain)

    # mở view cấu hình dự án
    def th_lpm_project_config_action(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_lpm2.th_lpm_project_form_action")
        action.update({
            'res_id': self.id,
            'view_mode': 'form',
            'context': {'th_type_status': 'short_status'},
        })
        return action

    # mở view khóa học
    def th_lpm_action_view_courses(self):
        # Sử dụng action có domain filter theo active_id để persist khi reload
        action = self.env["ir.actions.actions"]._for_xml_id("th_lpm2.th_lpm_course_action")

        # Xử lý context an toàn
        current_context = action.get('context', {})
        if isinstance(current_context, str):
            # Sử dụng safe_eval với context chứa active_id
            eval_context = {'active_id': self.id}
            try:
                current_context = safe_eval(current_context, eval_context) if current_context else {}
            except:
                # Nếu eval thất bại, sử dụng context mặc định
                current_context = {}

        # Merge context với thông tin project
        action['context'] = dict(current_context, **{
            'active_id': self.id,
            'active_model': 'th.project.lpm',
            'default_th_project_id': self.id,
            'create': 1,
        })

        return action

    # tính số lượng khóa học trong dự án
    @api.depends('th_course_ids')
    def _compute_course_count(self):
        for rec in self:
            rec.th_course_count = len(rec.th_course_ids)

    # kiểm tra mã và tên dự án có trùng lặp không
    @api.constrains('th_project_code')
    def _check_unique_code_name(self):
        for rec in self:
            domain = [('id', '!=', rec.id),('th_project_code', '=', rec.th_project_code)]
            if self.search_count(domain):
                raise ValidationError('Không được trùng mã dự án!')

    @api.model
    def _read_group_request_status(self, stages, domain, order):
        if self.env.context.get('th_type_status') == 'short_status':
            rec = self.env['th.project.status'].search([('th_type_status', '=', 'short_status')])
        else:
            rec = self.env['th.project.status'].search([('th_type_status', '=', 'long_status')])
        return rec

    # mở view cấu hình dự án
    def th_lpm_long_project_config_action(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_lpm2.th_lpm_long_project_form_action")
        action.update({
            'res_id': self.id,
            'view_mode': 'form',
            'context': {'th_type_status': 'long_status'},
        })
        return action

    # mở view học phần
    def th_lpm_action_view_long_courses(self):
        # Sử dụng action có domain filter theo active_id để persist khi reload
        action = self.env["ir.actions.actions"]._for_xml_id("th_lpm2.th_lpm_long_course_action")

        # Xử lý context an toàn
        current_context = action.get('context', {})
        if isinstance(current_context, str):
            # Sử dụng safe_eval với context chứa active_id
            eval_context = {'active_id': self.id}
            try:
                current_context = safe_eval(current_context, eval_context) if current_context else {}
            except:
                # Nếu eval thất bại, sử dụng context mặc định
                current_context = {}

        # Merge context với thông tin project
        action['context'] = dict(current_context, **{
            'active_id': self.id,
            'active_model': 'th.project.lpm',
            'default_th_project_id': self.id,
            'create': 1,
        })

        return action

    @api.model
    def create(self, vals):
        if self.env.user.has_group('th_lpm2.th_role_th_lpm2_employee'):
            raise ValidationError(_('Nhân viên không có quyền tạo mới.'))

        return super().create(vals)
