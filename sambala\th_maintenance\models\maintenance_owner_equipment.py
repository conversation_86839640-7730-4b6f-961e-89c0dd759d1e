from odoo import api, fields, models

class OwnerEquipment(models.Model):
    _name = 'th.owner.equipment'
    _description = 'Owner Equipment'
    _rec_name = 'name'

    _sql_constraints = [
        ('unique_name', 'unique(name)', 'Tê<PERSON> quyền sở hữu phải là duy nhất!')
    ]

    name = fields.Char(string="Quyền sở hữu", required=True)
    maintenance_equipment_ids = fields.One2many('maintenance.equipment', 'th_owner_equipment_id', string="Thiết bị")
    maintenance_equipment_count = fields.Integer(string="Số lượng thiết bị", compute='_compute_maintenance_equipment_count', store=False)

    @api.depends('maintenance_equipment_ids')
    def _compute_maintenance_equipment_count(self):
        for owner_equipment in self:
            owner_equipment.maintenance_equipment_count = len(owner_equipment.maintenance_equipment_ids)

    def th_action_maintenance_equipment(self):
        return {
            'type': 'ir.actions.act_window',
            'name': '<PERSON><PERSON> loại thiết bị',
            'res_model': 'maintenance.equipment.category',
            'view_mode': 'kanban',
            'view_id': self.env.ref('th_maintenance.view_equipment_category_kanban').id,
            'target': 'current',
            'context': {'default_th_owner_equipment_id': self.id},
        }