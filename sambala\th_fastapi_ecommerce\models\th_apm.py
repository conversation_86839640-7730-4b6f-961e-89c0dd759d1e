from odoo import fields, models, api


class ThApm(models.Model):
    _inherit = "th.apm"

    @api.model
    def get_or_create_partner(self, name, phone, email):
        partner = self.env['res.partner'].search([('phone', '=', phone)], limit=1)
        if not partner:
            partner = self.env['res.partner'].create({
                "name": name,
                "phone": str(phone),
                "email": email or '',
            })
        return partner

    @api.model
    def create_apm_lead_record(self, records, partner):
        campaign = self.env['th.apm.campaign'].search([('name', '=', records.campaign)], limit=1)
        channel = self.env['th.info.channel'].search([('name', '=', records.channel)], limit=1)
        product_ids = self.env['product.product'].search([('name', 'in', records.product)]).ids or []
        th_product_category_ids = self.env['product.category'].search([('name', '=', records.product_categ)]).ids or []
        create_uid = self.env["res.users"].search([('name', '=', records.create_uid)]).id

        values = {
            "th_origin_id": records.origin_id or False,
            "th_source_group_id": records.source_group_id or False,
            "th_source_name": records.source_group_name or False,
            "th_ownership_unit_id": records.ownership_id or False,
            "th_apm_dividing_ring_id": records.dviding_ring_id or False,
            "name": records.name or False,
            "th_partner_email": records.email or False,
            "th_partner_phone": records.phone or False,
            "th_partner_id": partner.id or False,
            "th_campaign_id": campaign.id if campaign else False,
            "th_product_ids": [(6, 0, product_ids)] if product_ids else False,
            "th_channel_id": channel.id if channel else False,
            "th_product_category_ids": [(6, 0, th_product_category_ids)] if th_product_category_ids else False,

        }
        lead = self.sudo().with_user(create_uid).create(values)
        if lead.th_apm_dividing_ring_id:
            lead.th_user_id = lead.th_apm_dividing_ring_id.action_assign_leads_dividing_ring()
        return lead


