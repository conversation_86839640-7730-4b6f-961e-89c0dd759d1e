import json
import xmlrpc.client
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime
import re

try:
    import qrcode
except ImportError:
    qrcode = None
try:
    import base64
except ImportError:
    base64 = None
from io import BytesIO


class ApmSaleOrder(models.Model):
    _inherit = "sale.order"

    th_apm_id = fields.Many2one('th.apm', string='Cơ hội APM', copy=False)
    th_introducer_id = fields.Many2one(comodel_name="res.partner", string="Người giới thiệu",
                                       related='th_apm_id.th_partner_reference_id', store=True)
    th_updated = fields.Boolean(string="Đã update", default=False)
    th_customer_name = fields.Char(string="Tên khách hàng")
    th_utm_medium = fields.Char("Utm Medium", tracking=True, copy=False)
    th_utm_campaign = fields.Char("Utm Campaign", tracking=True, copy=False)
    th_utm_source = fields.Char("Utm Source", tracking=True, copy=False)
    th_status = fields.Selection([('draft', 'Pending Payment'), ('processing', 'Processing'), ('completed', 'Completed'),
                                  ('canceled', 'Canceled'), ('refund', 'Refund')], copy=False, string="Status VMC",
                                 tracking=True, default="draft")
    th_order_vmc_name = fields.Char('Mã đơn hàng VMC', copy=False)
    th_api_to_vmc = fields.Boolean(default=False, help="Nếu true tức là đã đẩy trạng thái đơn hàng sang 2e thành công!")
    th_data_getfly = fields.Text(string="Getfly Order Data", copy=False)
    th_api_aff_order_id = fields.Integer(string="Id order Aff", copy=False)
    th_prepaid_amount = fields.Monetary(string='Đã trả trước', compute='_compute_prepaid_amount')
    th_apm_team_id = fields.Many2one(comodel_name="th.apm.team", string="Đội chăm sóc")
    th_apm_source_group_id = fields.Many2one(string="Nhóm nguồn", related="th_apm_id.th_source_group_id", )
    th_channel_id = fields.Many2one(string="Kênh", related="th_apm_id.th_channel_id", )
    th_origin_ids = fields.Many2many('th.origin', compute="compute_origin")
    th_refund_invoice_apm = fields.Boolean("Đơn hàng cần hoàn tiền apm", copy=False)
    th_refunded_invoice_apm = fields.Boolean("Đơn hàng đã hoàn tiền apm", copy=False, store=True)
    # th_paid_order = fields.Boolean('Đơn hàng đã thanh toán', compute="_compute_th_paid_order", store=True)
    # th_paid_order_compute = fields.Boolean(compute="_compute_th_paid_order")
    th_ecommerce_platform = fields.Boolean(related='th_apm_id.th_ecommerce_platform')
    qr_code = fields.Binary("QR Code", compute='generate_qr_code')
    th_status_payment_invoiced = fields.Selection(string='Trạng thái thanh toán', related="invoice_ids.payment_state",
                                                  store=True)
    th_payment_date = fields.Date(string='Ngày thanh toán', related='invoice_ids.invoice_date_due', store=True)
    th_status_refund_search = fields.Selection(string='Trạng thái hoàn tiền', related="th_status_refund", store=True)
    th_order_vmc_id = fields.Integer(string="ID order vmc", copy=False)
    th_ownership_unit_id = fields.Many2one(comodel_name="th.ownership.unit", string="Đơn vị sở hữu", tracking=True)
    # th_apm_active_account_ids = fields.Many2one(comodel_name="th.apm.active.account",string='Quản lý tài khoản')
    th_account_status =fields.Selection([('success', 'Thành công'), ('error', 'Lỗi')],  string="Trạng thái tài khoản")
    th_apm_active_account_ids =fields.Many2many('th.origin', compute="compute_origin")
    th_notification = fields.Boolean()
    th_total_amount =fields.Float(string="Tổng tiền chưa giảm giá" , compute="_compute_total_amount")
    th_apm_lead_name = fields.Char(string="Mã cơ hội APM", compute="_compute_th_apm_lead_name")
    th_origin_id = fields.Many2one(comodel_name='th.origin', string="Dòng sản phẩm", domain=[('th_module_ids.name', '=', 'APM')],store=True)
    ecm_type = fields.Selection(selection=[
            ('vmc', "VMC"),
            ('vstep', "VSTEP"),
            ('ome', "OM'E"),
        ],
        string="Đơn hàng được tạo từ trang")
    th_username = fields.Char("Username")
    th_is_a_simple_lesson = fields.Boolean(string="Là đơn học thử")
    th_cohort_id = fields.Many2one('th.cohort', string="Khóa tuyển sinh")
    th_is_sale_vstep = fields.Boolean( string='Là đơn hàng vstep',default=False)
    th_payment_method = fields.Selection([
        ('handmade', 'Thủ công'),
        ('auto', 'Tự động')
    ], string="Phương thức thanh toán")

    th_mail_invoice = fields.Boolean("Gửi email kế toán")
    th_cohort_class_id = fields.Many2one('th.cohort.class', string="Lớp")

    @api.onchange('th_origin_id')
    def _onchange_th_is_sale_vstep(self):
        for rec in self:
            if rec.th_origin_id and rec.th_origin_id.name in self.env.ref('th_setup_parameters.th_origin_vstep').name:
                rec.th_is_sale_vstep = True
            else:
                rec.th_is_sale_vstep = False
    @api.constrains('th_is_a_simple_lesson')
    def _check_trial_period(self):
        for record in self:
            if record.th_is_a_simple_lesson:
                now = datetime.now()
                for rec in record.order_line:
                    if not rec.product_template_id.preorder:
                        raise ValidationError("Bạn cần thiết lập ngày Preorder cho sản phẩm để xác định thời gian học thử.")
                    if now > rec.product_template_id.preorder:
                        raise ValidationError("Không thể chọn 'Là đơn học thử' vì thời gian học thử sản phẩm này đã kết thúc.")
    @api.depends('order_line.price_subtotal','order_line.price_total')
    def _compute_total_amount(self):
        for order in self:
            th_total_amount = sum(line.price_unit * line.product_uom_qty for line in order.order_line if not line.is_downpayment and line.product_id.default_code)
            order.th_total_amount = th_total_amount

    @api.depends('th_apm_id')
    def _compute_th_apm_lead_name(self):
        for rec in self:
            rec.th_apm_lead_name = False
            if rec.th_apm_id:
                rec.th_apm_lead_name = rec.th_apm_id.name.split(']')[0].strip('[')

    def generate_qr_code(self):
        for rec in self:
            if qrcode and base64:
                qr = qrcode.QRCode(
                    version=1,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=5,
                    border=6,
                )
                link_form_ecommerce = self.env['ir.config_parameter'].sudo().get_param('link_form_ecommerce', default="")
                qr.add_data(f"{link_form_ecommerce}?id_sale_order={rec.id}")
                qr.make(fit=True)
                img = qr.make_image()
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.update({'qr_code': qr_image})
    # @api.depends('invoice_ids')
    # def _compute_th_refunded_invoice_apm(self):
    #     for rec in self:
    #         if rec.invoice_ids:
    #             total_receive = sum(
    #                 self.env['account.move'].search([('id', 'in', rec.invoice_ids.ids)]).mapped(
    #                     'th_receive_amount'))
    #             total_refund = sum(
    #                 self.env['account.move'].search([('id', 'in', rec.invoice_ids.ids)]).mapped(
    #                     'th_refund_amount'))
    #             rec.th_refunded_invoice_apm = True if total_refund == total_receive else False
    #         else:
    #             rec.th_refunded_invoice_apm = False

    # @api.depends('invoice_ids')
    # def _compute_th_paid_order(self):
    #     for rec in self:
    #         rec.th_paid_order = False
    #         rec.th_paid_order_compute = False
    #         if rec.invoice_ids and rec.invoice_status == 'invoiced' and not rec.invoice_ids.filtered(
    #                 lambda d: d.payment_state != 'paid'):
    #             rec.th_paid_order = True
    #             rec.th_paid_order_compute = True

    @api.depends('order_line.price_unit')
    def _compute_prepaid_amount(self):
        for order in self:
            total_prepaid = sum(line.price_unit for line in order.order_line if line.is_downpayment)
            order.th_prepaid_amount = str(total_prepaid)

    def action_view_invoice(self):
        action = super().action_view_invoice()
        if self._context.get('view_apm', False):
            if len(self.mapped('invoice_ids')) == 1:
                action['target'] = 'new'
            action['context'] = {
                'create': False,
                'edit': False,
                'view_no_maturity': True,
                'default_th_invoice_phone': self.partner_id.phone,
                'default_th_invoice_email': self.partner_id.email,
            }
            if len(self.mapped('invoice_ids')) > 1:
                return {
                    'type': 'ir.actions.act_window',
                    'name': 'Hoá đơn',
                    'view_mode': 'tree,form',
                    'res_model': 'account.move',
                    'target': 'current',
                    'domain': [('id', 'in', self.mapped('invoice_ids').ids)],
                    'context': {'create': False, 'delete': False}
                }
        return action

    @api.model_create_multi
    def create(self, values):
        if self._context.get('sale_order_from_srm', False):
            sale_order = super(ApmSaleOrder, self).create(values)
        else:
            apm_lead = False
            for vals in values:
                if vals.get('th_order_vmc_id', False):
                    th_partner_reference_id = False
                    if vals.get('th_utm_source', False):
                        th_partner_reference_id = self.env['res.partner'].search(
                            [('th_affiliate_code', '=', vals.get('th_utm_source', False))], limit=1)
                    apm_state = self.env['th.apm.level'].search([('th_last_status', '=', True)], limit=1)
                    product_ids = []
                    if vals.get('order_line', False):
                        for prod in vals.get('order_line'):
                            product_ids.append(prod[2]['product_id'])
                    apm_lead = self.env['th.apm'].with_context(from_2e_sale_order=True).create({
                        'th_stage_id': apm_state.id,
                        'name': 'MỚI',
                        'th_partner_id': vals.get('partner_id', False),
                        'th_partner_reference_id': th_partner_reference_id.id if th_partner_reference_id else False,
                        'th_ownership_unit_id': self.env.ref('th_setup_parameters.th_aum_ownership_unit').id,
                        'th_status_detail_id': self.env.ref('th_apm.th_no_process_detail').id,
                        'th_status_category_id': self.env.ref('th_apm.th_no_process_category').id,
                        'th_source_group_id': self.env.ref('th_apm.th_no_process_source').id,
                        'th_product_ids': [(6, 0, product_ids)],
                        'th_campaign_id': self.env.ref('th_apm.campaign_lead_auto').id,
                        'th_origin_id': self.env.ref('th_setup_parameters.th_origin_vmc').id,
                    })
                    if apm_lead:
                        vals['th_origin_id'] = apm_lead.th_origin_id.id
                        values['th_username'] = apm_lead.th_partner_phone
            # if vals.get('order_line', False):
            #     for prod in vals.get('order_line'):
            #         prod[2]['name']='Mới'
            # Call the original create method to create the sale order
            sale_order = super(ApmSaleOrder, self).create(values)
            if sale_order.th_sale_order == 'apm':
                sale_order.name = 'APM-' + sale_order.name
                if sale_order.th_apm_id.th_user_id and sale_order.user_id and sale_order.th_apm_id.th_user_id.id != sale_order.user_id.id:
                    sale_order.user_id = sale_order.th_apm_id.th_user_id
            for rec in sale_order:
                if apm_lead:
                    rec.th_apm_id = apm_lead.id
                    rec.th_sale_order = 'apm'
                    sale_order.name = 'APM-' + sale_order.name
                    # sale_order.action_confirm()
                # Create sale order on Affiliate system
                if rec.th_introducer_id and not self._context.get('th_test_import', False):
                    rec.check_synchronization_aff_sale_order([], rec.th_introducer_id.th_affiliate_code)
                # Check if th_apm_id is present and related to an opportunity
                if rec.th_apm_id and rec.th_apm_id._name == 'th.apm':
                    opportunity = rec.th_apm_id
                    menu_id = self.env['ir.ui.menu'].search([('name', '=', 'Cơ hội')], limit=1)
                    log_note = _(
                        "Đơn hàng được tạo từ cơ hội: <a href='/web?#id=%s&model=th.apm&menu_id=%s&view_type=form'>%s</a>") % (
                                   opportunity.id, menu_id.id, opportunity.name)

                    # Add the log note to the chatter of the sale order
                    rec.message_post(body=log_note)
                # Check the order conditions created from apm
                if self._context.get('order_create_from_apm'):
                    # rec.partner_id.write({'th_is_order_apm': True})
                    # rec.action_confirm()
                    rec.write({'th_status': 'draft'})
                    # Create notification
                    # th_search_apm = self.env['th.apm'].search(
                    #     [('th_partner_id', '=', rec.partner_id.id), ('th_is_lead_TTVH', '=', True),
                    #      ('th_after_sales_care', '=', True), ('th_reason', '=', None), ('th_order_id', '=', False)],limit=1)
                    #
                    # if th_search_apm:
                    #     th_search_apm.message_post(
                    #         message_type='notification',
                    #         body='Khách hàng vừa mua thêm đơn hàng và đã được cập nhật vào lịch sử mua hàng',
                    #     )
                    # author_id = self.env.user.partner_id.id
                    # noti = {
                    #     'date': datetime.now(),
                    #     'author_id': author_id,
                    #     'message_type': 'notification',
                    #     'subtype_id': self.env['mail.message.subtype'].search([('name', '=', 'Ghi chú')]).id,
                    #     'model': 'th.apm',
                    #     'res_id': rec.th_apm_id.id,
                    #     'body': 'Khách hàng vừa mua thêm đơn hàng và đã được cập nhật vào lịch sử mua hàng',
                    # }
                    # self.env['mail.message'].create(noti)

                if rec.th_introducer_id and not rec.th_utm_source:
                    rec.th_utm_source = rec.th_introducer_id.th_affiliate_code

                if not rec.th_introducer_id and rec.th_utm_source:
                    rec.th_introducer_id = self.th_introducer_id.search([('th_affiliate_code', '=', rec.th_utm_source)],
                                                                        limit=1)
        return sale_order
    def write(self, values):
        # if self._context.get('th_updated', False):
        #     values.update({'th_updated': True})
        # note = _("Đơn hàng %s đã được cập nhật thông tin khách hàng", self.name)
        res = super(ApmSaleOrder, self).write(values)
        # for rec in self:
        #     if self._context.get('th_updated', False):
        #         account = self.env['th.apm.active.account'].search([('th_sale_order_id', '=', rec.id)])
        #         if rec.th_notification == False:
        #             if account:
        #                 account.write({'th_customer_name': rec.partner_id.name,
        #                                'th_customer_code': rec.partner_id.th_customer_code,
        #                                'th_customer_phone': rec.partner_id.phone,
        #                                'th_customer_email': rec.partner_id.email,
        #                                'th_sale_order_id': rec.id})
        #
        #             rec.activity_schedule(
        #                     'th_apm.th_apm_icon',
        #                     note=note,
        #                     user_id=rec.create_uid.id
        #                 )
        #             if rec.th_apm_id.th_apm_team_id.th_team_leader_id:
        #                 rec.activity_schedule(
        #                     'th_apm.th_apm_icon',
        #                     note=note,
        #                     user_id=rec.th_apm_id.th_apm_team_id.managẻ_id.id
        #                 )
        #             rec.th_notification = True


        if 'state' in values or 'th_apm_id' in values:
            for order in self:
                if order.th_apm_id and order.th_apm_id.th_after_sales_care:
                    # Cập nhật trạng thái cơ hội
                    order.th_apm_id.write({
                        'th_stage_id': self.env['th.apm.level'].search([('name', '=', 'L0')], limit=1).id,
                        'th_status_category_id': self.env['th.status.category'].search([('name', '=', 'Chưa xử lý')],
                                                                                       limit=1).id,
                        'th_status_detail_id': self.env['th.status.detail'].search([('name', '=', 'Chưa xử lý')],
                                                                                   limit=1).id,
                    })
        return res

    def unlink(self):
        for rec in self:
            active_account = self.env['th.apm.active.account'].sudo().search([('th_sale_order_id', '=', rec.id)])
            if active_account:
                active_account.sudo().unlink()
        return super().unlink()
    def th_syncss_order_vmc(self):
        result = self.th_create_order(rec=self)
        return result

    def action_confirm(self):
        self.ensure_one()
        if self.th_sale_order == 'apm':
            email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            for rec in self:
                if rec.th_customer_email and not re.match(email_regex, rec.th_customer_email) and rec.th_sale_order == "apm":
                    raise UserError("Email chưa đúng định dạng vui lòng sửa lại trước khi xác nhận đơn hàng !")
                res = super().action_confirm()

                if rec.th_apm_id and rec.th_apm_id.th_after_sales_care:
                    rec.th_apm_id.write({
                        'th_stage_id': self.env['th.apm.level'].search([('th_first_status', '=', True)], limit=1).id,
                        'th_status_category_id': self.env.ref('th_apm.th_no_process_category').id,
                        'th_status_detail_id': self.env.ref('th_apm.th_no_process_detail').id,
                    })
                    # Add this block to update invoice origin_id
                if rec.invoice_ids and rec.th_origin_id:
                    rec.invoice_ids.sudo().write({'th_apm_origin_name': rec.th_origin_id.name})

                if rec.state == 'sale' and rec.invoice_ids.th_payment_status == 'not_paid' and rec.th_apm_id:
                    self.env['th.apm.active.account'].create_record_from_sale_order(rec)
                return res
        else:
            res = super().action_confirm()
            return res

    def check_synchronization_aff_sale_order(self, data_code_aff=[], aff_code=None):
        data_to_send = {}
        record = self.search([('th_utm_source', 'in', data_code_aff)])
        if aff_code:
            self.ensure_one()
            record = self
        server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
                                                      order='id desc')
        try:
            if not server_api:
                raise ValidationError(_('Không tìm thấy server!'))
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api), allow_none=True)
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password
            for rec in record:
                data_to_send = {
                    'name': rec.name,
                    'th_customer': rec.partner_id.name,
                    'th_customer_code': rec.partner_id.th_customer_code,
                    'th_state_vmc': 'draft',
                    'th_date_order': fields.Datetime.now(),
                    'th_price': rec.amount_total,
                    'th_utm_source': aff_code if aff_code else rec.th_utm_source,
                    'th_affiliate_code': aff_code if aff_code else rec.th_utm_source,
                    'th_utm_medium': rec.th_utm_medium,
                    'th_utm_campaign': rec.th_utm_campaign,
                    'th_order_vmc_id': str(rec.th_order_vmc_id),
                    'th_product_ids': [(5, 0, 0)] + [(0, 0, {
                        'name': line.name,
                        'th_price': line.price_total,
                    }) for line in rec.order_line],
                }
                context = {
                    'module': self.env.ref('th_setup_parameters.th_apm_module').name,
                    'origin': rec.th_apm_id.th_origin_id.th_code,
                    'company_code': rec.th_apm_id.th_ownership_unit_id.th_code,
                    'th_create': True if not rec.th_api_aff_order_id else False,
                    'th_api_aff_order_id': [rec.th_api_aff_order_id] if rec.th_api_aff_order_id else [],
                }
                if rec.th_api_aff_order_id:
                    data_to_send = {'th_state_vmc': 'draft'}
                    if not aff_code and not rec.invoice_ids.filtered(lambda m: m.state != 'posted'):
                        data_to_send = {'th_state_vmc': 'completed'}
                res_id = result_apis.execute_kw(db, uid_api, password, 'th.aff.order', 'receive_data_order',
                                                [[], data_to_send], {'context': context})
                if res_id.get('id', False):
                    rec.write({'th_api_aff_order_id': int(res_id.get('id', False))})
        except Exception as e:
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str(record.id),
                'th_input_data': str(data_to_send),
                'th_function_call': str('check_synchronization_aff_sale_order'),
            })
        return True

    @api.model
    def async_data_from_b2b_template(self,data=None):
        try:
            sale_order = self.env['sale.order'].sudo().search([('id','=',data.get('sale_id'))])
            if sale_order:
                partner = self.env['res.partner'].sudo().search([('phone', '=', data.get('phone'))],limit =1)

                if partner:
                    vals_id = {'partner_id':partner.id,'th_updated': True}
                    vals_partner_id = {'th_partner_id': item['id'] for item in partner}
                    th_apm_id = sale_order.th_apm_id
                    sale_id = int(data.get('sale_id'))

                    write_apm = self.env['th.apm'].sudo().browse(th_apm_id.id).write(vals_partner_id)

                    # write_sale = self.env['sale.order'].sudo().browse(sale_id).with_context(th_update=True).write(vals_id)
                    sale_order_list = self.env['sale.order'].sudo().search([('th_apm_id', '=', th_apm_id.id)])
                    if sale_order_list:
                        for rec in sale_order_list:
                            rec.sudo().with_context(th_updated=True).write(vals_id)
                            if sale_order.th_notification == False:
                                note = _("Đơn hàng %s đã được cập nhật thông tin khách hàng", sale_order.name)
                                account = self.env['th.apm.active.account'].search(
                                    [('th_sale_order_id', '=', sale_order.id)])
                                if account:
                                    account.write({'th_customer_name': sale_order.partner_id.name,
                                                   'th_customer_code': sale_order.partner_id.th_customer_code,
                                                   'th_customer_phone': sale_order.partner_id.phone,
                                                   'th_customer_email': sale_order.partner_id.email,
                                                   'th_sale_order_id': sale_order.id})

                                sale_order.activity_schedule(
                                    'th_apm.th_apm_icon',
                                    note=note,
                                    user_id=sale_order.create_uid.id
                                )
                                if sale_order.th_apm_id.th_apm_team_id.manager_id:
                                    sale_order.activity_schedule(
                                        'th_apm.th_apm_icon',
                                        note=note,
                                        user_id=sale_order.th_apm_id.th_apm_team_id.manager_id.id
                                    )
                            if rec.invoice_ids:
                                for invoice_id in rec.invoice_ids:
                                    # write_invoice = invoice_id.sudo().with_context(check_move_validity=False).write(
                                    #     vals_id)
                                    try:
                                        # Cập nhật partner_id bằng SQL
                                        self.env.cr.execute("""
                                            UPDATE account_move
                                            SET partner_id = %s
                                            WHERE id = %s
                                        """, (vals_id.get('partner_id'), invoice_id.id))
                                    except Exception as e:
                                        raise UserError(f"Lỗi cập nhật hóa đơn {invoice_id.id}: {str(e)}")
                                invoices = self.env['account.move'].browse(rec.invoice_ids.ids)
                                invoices.recompute()
                                invoices.write({"partner_shipping_id": partner.id,
                                                "th_customer_code": partner.th_customer_code,
                                                "invoice_partner_display_name":partner.name})

                else:
                    partner_id = sale_order.partner_id
                    partner = partner_id.write({
                        'name':data.get('name'),
                        'phone': data.get('phone'),
                        'email': data.get('email')
                    })
                    vals_id = {'th_updated': True}
                    write_sale = sale_order.write(vals_id)
                    if sale_order.th_notification == False:
                        note = _("Đơn hàng %s đã được cập nhật thông tin khách hàng", sale_order.name)
                        account = self.env['th.apm.active.account'].search([('th_sale_order_id', '=', sale_order.id)])
                        if account:
                            account.write({'th_customer_name': sale_order.partner_id.name,
                                           'th_customer_code': sale_order.partner_id.th_customer_code,
                                           'th_customer_phone': sale_order.partner_id.phone,
                                           'th_customer_email': sale_order.partner_id.email,
                                           'th_sale_order_id': sale_order.id})

                        sale_order.activity_schedule(
                            'th_apm.th_apm_icon',
                            note=note,
                            user_id=sale_order.create_uid.id
                        )
                        if sale_order.th_apm_id.th_apm_team_id.manager_id:
                            sale_order.activity_schedule(
                                'th_apm.th_apm_icon',
                                note=note,
                                user_id=sale_order.th_apm_id.th_apm_team_id.manager_id.id
                            )
            return True
        except Exception as e:
            raise ValidationError(e)

    # @api.depends('th_apm_id.th_partner_reference_id')
    # def _compute_introducer_id(self):
    #     for order in self:
    #         order.th_introducer_id = order.th_apm_id.th_partner_reference_id

    def sync_order_vmc(self, values):
        th_category_id = False
        origin = False
        product_line = []
        th_partner_reference = False
        try:
            res_partner = self.env['res.partner'].search([('phone', '=', values.get('phone', ' nophone '))])
            if not res_partner and values.get('phone', False):
                res_partner = self.env['res.partner'].sudo().create({
                    'name': values.get('customer_name', False),
                    'phone': values.get('phone', False),
                    'email': values.get('email', False),
                    'th_module_ids': [(4, self.env.ref('th_setup_parameters.th_apm_module').id)],
                })
            if not res_partner:
                return False

            if values.get('th_utm_source', False):
                th_partner_reference = self.env['res.partner'].search(
                    [('th_affiliate_code', '=', values.get('th_utm_source'))]).id
            apm_lead = self.env['th.apm'].search(
                [('th_order_vmc_name', '=', values.get('vmc_name', ' khong co lead nay '))])

            if not apm_lead:
                if values.get('vmc', False):
                    origin = self.env.ref('th_setup_parameters.th_origin_vmc').id

                apm_state = self.env['th.apm.level'].search([('th_last_status', '=', True)])
                apm_lead = self.env['th.apm'].with_context(lead_auto=True).create({
                    'th_stage_id': apm_state.id,
                    'name': 'MỚI',
                    'th_partner_id': res_partner.id,
                    'th_partner_reference_id': th_partner_reference,
                    'th_order_vmc_name': values.get('vmc_name', False),
                    'th_campaign_id': self.env.ref('th_apm.campaign_lead_auto').id,
                    'th_origin_id': origin
                })

            for line_product in values.get('line_products', []):
                th_product_templ = False

                if line_product['data_product'].get('default_code', '  '):
                    th_product_templ = self.env['product.template'].search(
                        [('default_code', '=', line_product['data_product'].get('default_code', '  '))])

                if line_product['data_product'].get('categ_id', False):
                    th_category_id = self.env['product.category'].search(
                        [('name', '=', line_product['data_product'].get('categ_id'))]).id

                    if not th_category_id:
                        th_category_id = self.env['product.category'].sudo().create({
                            'name': line_product['data_product'].get('categ_id')
                        }).id

                if not th_product_templ and line_product['data_product'].get('name', False):
                    th_product_templ = self.env['product.template'].sudo().create({
                        'name': line_product['data_product'].get('name', False),
                        'sale_ok': line_product['data_product'].get('sale_ok', False),
                        'purchase_ok': line_product['data_product'].get('purchase_ok', False),
                        'list_price': line_product['data_product'].get('list_price', False),
                        'default_code': line_product['data_product'].get('default_code', False),
                        'categ_id': th_category_id,
                        'invoice_policy': 'order',
                        'detailed_type': 'consu',

                    })

                product_line.append((0, 0, {
                    'product_template_id': th_product_templ.id,
                    'product_uom_qty': line_product.get('product_uom_qty', 1),
                    'name': f'[{th_product_templ.default_code}]' + th_product_templ.name,
                    'price_unit': line_product.get('price_unit', 0),
                    'tax_id': [[6, False, []]],
                    'product_id': th_product_templ.product_variant_id.id,
                    'display_type': False
                }))

            sale_order = self.env['sale.order'].search(
                [('th_order_vmc_name', '=', values.get('vmc_name', ' khong co don hang'))])
            val_sale = {
                'partner_id': res_partner.id,
                'th_apm_id': apm_lead.id,
                'th_order_vmc_id': values.get('th_order_vmc_id', False),
                'th_origin_id': apm_lead.th_origin_id.id,
                'state': values.get('th_state', 'sale'),
                'th_utm_source': values.get('th_utm_source', False),
                'th_utm_campaign': values.get('th_utm_campaign', False),
                'th_utm_medium': values.get('th_utm_medium', False),
                'th_introducer_id': th_partner_reference,
                'th_status': values.get('th_status_vmc', False),
                'th_order_vmc_name': values.get('vmc_name', False),
                'order_line': product_line if not sale_order else [(5, 0, 0)] + product_line,
            }
            if not sale_order:
                sale_order = self.env['sale.order'].with_context(order_create_from_apm=True).create(val_sale)
            else:
                sale_order = self.env['sale.order'].with_context(order_create_from_apm=True).write(val_sale)

            return {'status': 'success'}
        except Exception as e:
            print(e)
            return {'status': 'error'}

        # return sale_order, res_partner, apm_lead

    # def sync_order_status_vmc(self):
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'vmc')], limit=1,
    #                                                   order='id desc')
    #     if not server_api:
    #         return False
    #     try:
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api), allow_none=True)
    #     except Exception as e:
    #         print(e)
    #         return e
    #     db = server_api.th_db_api
    #     uid_api = server_api.th_uid_api
    #     password = server_api.th_password
    #     vals = {}
    #     for rec in self:
    #         vals[f"{rec.th_order_vmc_id}"] = {'state': self.state, 'status': self.th_status}
    #     try:
    #         th_status_order = result_apis.execute_kw(db, uid_api, password, 'sale.order', 'action_order_status',
    #                                                  [[], vals])
    #     except Exception as e:
    #         print(e)

    def th_action_sync_orders(self):
        order_ids = False
        server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'vmc')], limit=1,
                                                      order='id desc')
        try:
            if not server_api:
                raise ValidationError('Không tìm thấy server!')
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api), allow_none=True)

            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password
            order_ids = self.search([('th_order_vmc_id', '!=', False)]).ids
            th_status_order = result_apis.execute_kw(db, uid_api, password, 'sale.order', 'th_action_sync_orders',
                                                     [[], order_ids])
        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str(self.ids),
                'th_input_data': str(order_ids),
                'th_function_call': str('th_action_sync_orders'),
            })

    def action_cancel(self):
        res = super().action_cancel()
        for rec in self:
            if rec.state == 'cancel':
                 rec.write({'th_status': 'canceled'})
        return res

    def action_order_details(self):
        self.ensure_one()
        action = self.env["ir.actions.actions"]._for_xml_id("th_apm.sale_orders_action")
        action['views'] = [[self.env.ref('th_apm.th_view_order_form').id, "form"]]
        action['res_id'] = self.id
        return action

    @api.depends('th_apm_id')
    def compute_origin(self):
        for rec in self:
            rec.th_origin_ids = False
            if rec.th_apm_id.th_origin_id:
                rec.th_origin_ids = [(6, 0, [rec.th_apm_id.th_origin_id.id])]
            if rec.th_apm_id.th_origin_ids:
                rec.th_origin_ids = [(6, 0, rec.th_apm_id.th_origin_ids.ids)]

    # can thanh toans
    @api.model
    def action_th_need_payment(self):

        return {
            'type': 'ir.actions.act_window',
            'name': 'Đơn hàng cần thanh toán',
            'view_mode': 'tree,form',
            'res_model': 'sale.order',
            'view_ids': [(self.env.ref("th_apm.view_order_tree").id, 'tree'),
                         (self.env.ref("th_apm.th_view_order_form").id, 'form')],
            'domain': [('th_paid_order', '=', False), ('th_apm_id', '!=', False)],

        }

    # da thanh toans
    @api.model
    def action_th_account_paid_orders(self):
        # domain = [('id', 'in', self.env['sale.order'].search([('th_apm_id', '!=', False), ('invoice_status', '=', 'invoiced')]).mapped('invoice_ids').filtered(lambda m: m.state == 'posted').mapped('invoice_line_ids').mapped('sale_line_ids').mapped('order_id').ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': 'Đơn hàng đã thanh toán',
            'view_mode': 'tree,form',
            'res_model': 'sale.order',
            'view_ids': [(self.env.ref("th_apm.view_order_tree").id, 'tree'),
                         (self.env.ref("th_apm.th_view_order_form").id, 'form')],
            'domain': [('th_paid_order', '!=', False), ('th_apm_id', '!=', False)],

        }

    def th_create_invoice(self):
        if self.filtered(lambda d: not d.order_line):
            raise ValidationError(_("Tồn tại đơn hàng chưa có sản phẩm. Vui lòng kiểm tra lại"))
        for rec in self:
            rec.action_confirm()
            invoice = rec._create_invoices(rec)
            invoice.th_account_move = 'apm'
            if rec.th_origin_id:
                invoice.th_apm_origin_name = rec.th_origin_id.name
            invoice.action_post()
        return self.action_th_account_no_invoice()

    def action_refund_apm(self):
        for rec in self:
            if not rec.invoice_ids:
                raise ValidationError("Đơn hàng chưa có hóa đơn cần hoàn tiền, vui lòng xem lại!")
            else:
                rec.th_refund_invoice_apm = True
                rec.th_refunded_invoice_apm = False

    def action_show_popup_refund_apm(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_apm.action_view_popup_refund_th_invoice_apm")
        action['context'] = {'default_order_id': self.id}
        return action

    # @api.depends('partner_id', 'th_apm_id.th_user_id')
    # def _compute_user_id(self):
    #     super()._compute_user_id()
    #     for rec in self:
    #         if rec.th_sale_order == 'apm' and rec.th_apm_id:
    #             rec.user_id = rec.th_apm_id.th_user_id.id
    @api.onchange('th_cohort_id')
    def _onchange_th_cohort_class_id(self):
        for rec in self:
            rec.th_cohort_class_id = False


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    th_partner_ref = fields.Char(related="product_id.partner_ref", string='Tên sản phẩm', store=True)
    is_reward_line = fields.Boolean('Is a program reward line', compute='_compute_is_reward_line', store=True)
    th_allocation_value = fields.Float(string="Giá trị phân bổ", compute='_compute_th_allocation_value', store=True)

    @api.depends('order_id.th_total_amount')
    def _compute_th_allocation_value(self):
        for rec in self:
            if rec.order_id.th_total_amount > 0 and rec.product_id.default_code:
                th_allocation_value = (rec.price_unit * rec.product_uom_qty) / rec.order_id.th_total_amount
                rec.th_allocation_value = th_allocation_value
            else:
                rec.th_allocation_value = 0
