from odoo import fields, models

class ZnsParam(models.Model):
    _name = 'th.zns.param'
    _description = 'ZNS Param'

    # Nhãn tham số
    name = fields.Char('Nhãn tham số', store=True, required=True)
    # Tham số
    th_key = fields.Char('Tham số', store=True, required=True)
    # Giới hạn
    th_limit = fields.Integer('Giới hạn', store=True, required=True)

    # <PERSON><PERSON><PERSON> buộc "Tham số" chỉ tồn tại duy nhất một lần
    _sql_constraints = [
        ('unique_th_key', 'UNIQUE(th_key)', 'Tham số chỉ tồn tại duy nhất một lần!')
    ]

