from typing import Annotated, List
from ..schemas import APMLeadDatas
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Path, Query
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
from odoo.exceptions import UserError
import logging
import json
from datetime import date

router = APIRouter(tags=["APM"])
_logger = logging.getLogger(__name__)

def log_api(env, state, description, input_data, function_name):
    env['th.log.api'].sudo().create({
        'state': state,
        'th_model': 'sale.order',
        'th_description': description,
        'th_input_data': str(input_data),
        'th_function_call': function_name,
        'is_log_fast_api': True,
    })




@router.post("/api/apm/leads")
def create_apm_lead(
    records: APMLeadDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        env = fastapi.env
        if not records.name or not records.phone or not records.create_uid:
            raise HTTPException(status_code=400, detail="Thiếu thông tin tạo cơ hội")

        if not fastapi:
            log_api(env, 'success', 'Cập nhật thành công', records, 'create_apm_lead')
            return [{"status": "failed", "response": "invalid fastapi"}]

        partner = env['th.apm'].get_or_create_partner(records.name, records.phone, records.email)
        lead = env['th.apm'].create_apm_lead_record(records, partner)

        return [{"status": "success", "response": "ok", "id": lead.id}]

    except UserError as e:
        log_api(env, 'error', f"Lỗi khi tạo cơ hội: {str(e)}", records, 'create_apm_lead')
        raise HTTPException(status_code=400, detail=str(e))

