from odoo import models, fields

class ZnsTeam(models.Model):
    _name = 'th.zns.team'
    _description = 'ZNS Team'

    # Tên đội
    name = fields.Char('Tên đội', required=True)
    # Trưởng nhóm
    th_leader_id = fields.Many2one(comodel_name='res.users', string='Trưởng nhóm', store=True)
    # Thành viên
    th_member_ids = fields.Many2many(comodel_name='res.users', string='Thành viên')
    # Tài khoản OA
    th_oa_ids = fields.One2many(comodel_name='th.zalo.account', inverse_name='th_oa_id', string='Tài khoản OA')