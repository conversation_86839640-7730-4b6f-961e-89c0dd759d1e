# -*- coding: utf-8 -*-
from odoo import api, fields, models

class MaintenanceEquipmentCategory(models.Model):
    _inherit = 'maintenance.equipment.category'

    maintenance_team_ids = fields.Many2many('maintenance.team', string="Responsible Team")
    th_specifications_ids = fields.Many2many('th.specification.type', string="Thông số kỹ thuật")
    th_asset_type_id = fields.Many2one('th.asset.type', string="Loại tài sản")

    th_equipment_count = fields.Integer(string="Số thiết bị", compute='_compute_filtered_equipment_count', store=False)

    @api.depends('equipment_ids')
    def _compute_filtered_equipment_count(self):
        owner_id = self.env.context.get('default_th_owner_equipment_id')
        for rec in self:
            if owner_id:
                rec.th_equipment_count = len(rec.equipment_ids.filtered(lambda e: e.th_owner_equipment_id.id == owner_id))
            else:
                rec.th_equipment_count = len(rec.equipment_ids)

    def action_open_equipments(self):
        sim_category = self.env['maintenance.equipment.category'].search(
            [('name', 'ilike', 'sim thẻ')], limit=1
        )

        group_by = 'th_sim_states' if self.id == sim_category.id else 'th_equipment_states'

        return {
            'type': 'ir.actions.act_window',
            'name': 'Thiết bị theo loại',
            'res_model': 'maintenance.equipment',
            'view_mode': 'kanban,tree,form',
            'views': [
                (self.env.ref('th_maintenance.view_maintenance_equipment_kanban_custom_group').id, 'kanban'),
                (False, 'tree'),
                (False, 'form'),
            ],
            'domain': [
                ('category_id', '=', self.id),
                ('th_owner_equipment_id', '=', self.env.context.get('default_th_owner_equipment_id')),
            ],
            'context': {
                'default_category_id': self.id,
                'group_by': group_by,
            },
        }