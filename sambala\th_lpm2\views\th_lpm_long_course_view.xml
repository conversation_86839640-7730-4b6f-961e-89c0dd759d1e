<odoo>
    <record id="th_long_course_lpm2_tree_view" model="ir.ui.view">
        <field name="name">th_long_course_lpm2_tree_view</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <tree>
                <header>
                    <button name="th_action_update_course_wizard"
                            type="object"
                            string="Cập nhật"
                            class="btn-primary"/>
                </header>
                <field name="th_course_code" string="Mã học phần"/>
                <field name="th_project_template_id" string="Tên học phần"/>
                <field name="th_aum_code"/>
                <field name="th_credit"/>
                <field name="th_production_start_date"/>
                <field name="th_production_end_date"/>
                <field name="th_opening_date"/>
                <field name="th_lecturer_id"/>
                <field name="th_level_production_id"/>
                <field name="th_lecturer_status"/>
                <field name="th_production_status"/>
                <field name="th_evaluation_status"/>
                <field name="th_printing_status"/>
            </tree>
        </field>
    </record>

    <record id="th_long_course_lpm2_form_view" model="ir.ui.view">
        <field name="name">th_long_course_lpm2_form_view</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="th_level_production_id" widget="th_statusbar" class="o_field_statusbar"
                           options="{'clickable':'1'}"
                           domain="[('th_type_production', '=', 'long_production')]"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="th_course_code" string="Mã học phần" required="1"/>
                            <field name="th_project_template_id" string="Tên học phần" required="1" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_aum_code"/>
                            <field name="th_credit" required="1"/>
                            <field name="th_type" required="1"/>
                            <field name="th_language" required="1"/>
                            <field name="th_object_type" required="1"/>
                            <field name="th_production_start_date"/>
                            <field name="th_production_end_date"/>
                            <field name="th_level_date"/>
                        </group>
                        <group>
                            <field name="th_origin_id" options="{'no_create': True,'no_edit': True, 'no_open':True}" required="1"/>
                            <field name="th_user_id_domain" invisible="1"/>
                            <field name="th_user_id" options="{'no_create': True,'no_edit': True, 'no_open':True}" domain="th_user_id_domain" required="1"/>
                            <field name="th_major_ids" widget="many2many_tags" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>

                            <field name="th_opening_date"/>
                            <field name="th_lecturer_id" required="1" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_lecturer_status"/>
                            <field name="th_production_status"/>
                            <field name="th_evaluation_status"/>
                            <field name="th_printing_status"/>
                            <field name="th_progress"/>
                            <field name="th_target"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Tiến độ sản xuất">
                            <button name="th_action_open_import_production_stage" string="Import" type="object" class="btn-primary"/>
                            <button name="th_action_export_data_production_stage" string="Export" type="object" class="btn-primary"/>
                            <field name="th_stage_ids">
                                <tree editable="bottom">
                                    <field name="th_content_production"/>
                                    <field name="th_required_quantity"/>
                                    <field name="th_lecturer_submitted"/>
                                    <field name="th_editing_completed"/>
                                    <field name="th_delivery_completed"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Thông tin chung">
                        </page>
                        <page string="Tiêu chuẩn sản xuất">
                            <field name="th_production_standard"/>
                        </page>
                        <page string="Sản xuất">

                        </page>
                        <page string="Lịch sử thay đổi">

                        </page>
                        <page string="File đính kèm">
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    <record id="th_long_course_lpm2_search_view" model="ir.ui.view">
        <field name="name">th.long.course.lpm2.search</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <search>
                <!-- Level Filters -->
                <filter string="L0" name="level_l0"
                        domain="[('th_level_production_id.th_level_code', '=', 'L0')]"/>
                <filter string="L1" name="level_l1"
                        domain="[('th_level_production_id.th_level_code', '=', 'L1')]"/>
                <filter string="L2" name="level_l2"
                        domain="[('th_level_production_id.th_level_code', '=', 'L2')]"/>
                <filter string="L3" name="level_l3"
                        domain="[('th_level_production_id.th_level_code', '=', 'L3')]"/>
                <filter string="L4" name="level_l4"
                        domain="[('th_level_production_id.th_level_code', '=', 'L4')]"/>
                <filter string="L5" name="level_l5"
                        domain="[('th_level_production_id.th_level_code', '=', 'L5')]"/>
                <filter string="L6" name="level_l6"
                        domain="[('th_level_production_id.th_level_code', '=', 'L6')]"/>
                <filter string="L7" name="level_l7"
                        domain="[('th_level_production_id.th_level_code', '=', 'L7')]"/>
                <filter string="L8" name="level_l8"
                        domain="[('th_level_production_id.th_level_code', '=', 'L8')]"/>
                <separator/>

                <!-- Lecturer Status Filters -->
                <filter string="Đã có GV" name="lecturer_available"
                        domain="[('th_lecturer_status', '=', 'available')]"/>
                <filter string="Chưa có GV" name="lecturer_not_available"
                        domain="[('th_lecturer_status', '=', 'not_available')]"/>
                <separator/>

                <!-- Production Status Filters -->
                <filter string="Chưa sản xuất" name="prod_not_started"
                        domain="[('th_production_status', '=', 'not_started')]"/>
                <filter string="Đang sản xuất" name="prod_in_progress"
                        domain="[('th_production_status', '=', 'in_progress')]"/>
                <filter string="Đã sản xuất" name="prod_done"
                        domain="[('th_production_status', '=', 'done')]"/>
                <separator/>

                <!-- Evaluation Status Filters -->
                <filter string="Chưa gửi" name="eval_not_sent"
                        domain="[('th_evaluation_status', '=', 'not_sent')]"/>
                <filter string="Đã gửi" name="eval_sent"
                        domain="[('th_evaluation_status', '=', 'sent')]"/>
                <filter string="Chưa duyệt" name="eval_not_approved"
                        domain="[('th_evaluation_status', '=', 'not_approved')]"/>
                <filter string="Đã duyệt" name="eval_approved"
                        domain="[('th_evaluation_status', '=', 'approved')]"/>
                <separator/>

                <!-- Printing Status Filters -->
                <filter string="Chưa in" name="print_not_printed"
                        domain="[('th_printing_status', '=', 'not_printed')]"/>
                <filter string="Đã in" name="print_printed"
                        domain="[('th_printing_status', '=', 'printed')]"/>
                <filter string="Bàn giao kế toán" name="print_delivered_accounting"
                        domain="[('th_printing_status', '=', 'delivered_accounting')]"/>
                <filter string="Bàn giao trường" name="print_delivered_school"
                        domain="[('th_printing_status', '=', 'delivered_school')]"/>
                <separator/>

                <!-- Opening Date Filter -->
                <filter string="Ngày lên môn" name="opening_date" date="th_opening_date"/>
                <searchpanel>
                    <field name="th_level_production_id" icon="fa-star" enable_counters="1"/>
                    <field name="th_user_id" icon="fa-user" enable_counters="1"/>
                    <field name="th_lecturer_status" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_printing_status" icon="fa-phone" enable_counters="1"/>
                    <field name="th_production_status" icon="fa-check" enable_counters="1"/>
                    <field name="th_evaluation_status" icon="fa-user-plus" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="th_lpm_long_course_action" model="ir.actions.act_window">
        <field name="name">Học phần</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.course.lpm2</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_long_course_lpm2_tree_view')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_long_course_lpm2_form_view')})]"/>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="th_long_course_lpm2_search_view"/>
        <field name="domain">[('th_project_id', '=', active_id)]</field>
        <field name="context">{'default_th_type_course': 'long', 'default_th_project_id': active_id, 'create': 1}</field>
        
    </record>
</odoo>
