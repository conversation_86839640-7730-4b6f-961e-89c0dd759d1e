from odoo import models, fields

class ZnsTemplateParam(models.Model):
    _name = "th.zns.template.param"
    _description = "Zns Template Param"

    th_accept_null = fields.<PERSON><PERSON>an(string="Cho phép để trống", store=True)
    th_param_id = fields.Many2one(
        comodel_name="th.zns.param", string="Nhãn tham số", store=True
    )
    th_key_param = fields.Char(
        related="th_param_id.th_key", string="Tham số", store=True
    )
    th_is_used = fields.<PERSON><PERSON><PERSON>(string="Là tham số đã được sử dụng", store=True)
    th_default_value = fields.Char(string="Dữ liệu mẫu", store=True)
