from odoo import models, fields


class ThProduction(models.Model):
    _name = 'th.office.production'
    _description = "Phòng sản xuất"

    name = fields.Char(string="Tên phòng")
    th_manager_id = fields.Many2one(comodel_name="res.users", string="<PERSON>u<PERSON>n lý")
    th_user_ids = fields.Many2many(
        comodel_name='res.users',
        string="Nhân sự",
    )

class ThCourseStatus(models.Model):
    _name = 'th.course.status'
    _description = "Trạng thái học phần"

    name = fields.Char(string="Tên trạng thái")
    th_description = fields.Text(string="Mô tả")

class ThLevelProduction(models.Model):
    _name = 'th.level.production'
    _description = "Level sản xuất"
    _rec_name = 'th_level_code'
    _order = 'th_sequence asc'

    th_level_code = fields.Char(string="Mã level")
    th_sequence = fields.Integer(string="", default=1)
    th_type_production = fields.Selection(
        selection=[
            ('short_production', 'Ngắn hạn'),
            ('long_production', 'Dài hạn'),
        ],
        string="Loại chương trình áp dụng",
    )

class ThStatusProject(models.Model):
    _name = 'th.project.status'
    _description = "Trạng thái dự án"


    name = fields.Char(string="Trạng thái")
    th_type_status = fields.Selection(
        selection=[
            ('short_status', 'Ngắn hạn'),
            ('long_status', 'Dài hạn'),
        ],
        string="Loại trạng thái áp dụng",
    )