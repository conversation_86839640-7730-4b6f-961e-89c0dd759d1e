from odoo import api, fields, models, _

from pytz import timezone, UTC

class ThCareHistory(models.TransientModel):
    _name = "th.care.history.vstep"
    _description = "L<PERSON>ch sử chăm sóc vstep"

    name = fields.Char(string="<PERSON>ên c<PERSON> hội", compute='_compute_preview', readonly=True)
    th_apm_lead_old_id = fields.Many2one(comodel_name="th.apm", string="Cơ hội gốc")
    th_apm_lead_new_id = fields.Many2one(comodel_name="th.apm", string="Cơ hội mới")
    preview = fields.Html(compute='_compute_preview',
                          sanitize=False,
                          sanitize_tags=False,
                          sanitize_attributes=False,
                          sanitize_style=False,
                          sanitize_form=False,
                          strip_style=False,
                          strip_classes=False)
    user_id = fields.Many2one("res.users", string="Người phụ trách", readonly=True)

    @api.depends('th_apm_lead_old_id')
    def _compute_preview(self):
        for rec in self:
            name, preview, user_id = self.th_check_message(rec)
            rec.preview = preview
            rec.name = name
            rec.user_id = user_id

    def th_check_message(self, rec):
        preview = ''
        name = ''
        user_id = False

        # Xử lý context sync_b2b và lấy cơ hội
        th_apm_lead_old_id = rec.th_apm_lead_old_id.sudo()
        name += th_apm_lead_old_id.name
        user_id = th_apm_lead_old_id.th_user_id.id

        # Lấy lịch sử tin nhắn
        messages = self.env['mail.message'].sudo().search(
            [('res_id', '=', th_apm_lead_old_id.id), ('model', '=', 'th.apm')], order='id asc'
        )

        for mes in messages:
            mes_date = UTC.localize(mes.date).astimezone(timezone(self.env.user.tz or 'Asia/Ho_Chi_Minh')).replace(tzinfo=None)
            # Xử lý các loại tin nhắn khác nhau

            if self.env.ref('mail.mt_note').id == mes.subtype_id.id or self.env.ref('th_apm.mt_lead_stage').id == mes.subtype_id.id:
                if not mes.body and mes.tracking_value_ids:
                    add_mes = ""
                    for track in mes.tracking_value_ids:
                        field_value = track.old_value_char or track.old_value_datetime or track.old_value_float or track.old_value_integer or track.old_value_text or 'Không'
                        new_value = track.new_value_char or track.new_value_datetime or track.new_value_float or track.new_value_integer or track.new_value_text or 'Không'
                        add_mes += f"{track.field_desc}: {field_value} --> {new_value}<br/>"
                    if add_mes:
                        preview = f"{add_mes}<br/>" + preview
                else:
                    preview = f"<b>{mes.author_id.name}</b>: vào <i>{mes_date}</i> đã ghi chú {mes.body} <br/>" + preview
            elif self.env.ref("mail.mt_comment").id == mes.subtype_id.id:
                preview = f"<b>{mes.author_id.name}</b>: vào <i>{mes_date}</i> đã gửi thư {mes.body} <br/>" + preview

        return name, preview, user_id