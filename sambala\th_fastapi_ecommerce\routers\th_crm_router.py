from typing import Annotated, List
from odoo.http import request
import time
from ..schemas import CRMLeadDatas
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Path, Query
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
from odoo.exceptions import UserError
import logging
import json
from datetime import date
router = APIRouter(tags=["CRM"])
_logger = logging.getLogger(__name__)

def log_crm_api_result(env, fastapi_id: int, fastapi_name: str, input_data: dict, success: bool, message: str, function_call: str = "", response_time: float = 0.0):
    state = 'success' if success else 'fail'

    try:
        env['th.log.api'].sudo().create({
            'state': state,
            'th_model': fastapi_name,
            'th_description': message,
            'th_function_call': function_call,
            'th_time_response': response_time,
            'is_log_fast_api': True,
            'th_input_data': json.dumps(input_data, ensure_ascii=False),
            'th_fastapi_endpoint_id': fastapi_id,
        })
    except Exception as e:
        # Log để tránh crash
        _logger.error(f"Lỗi ghi log CRM API: {e}")


@router.post("/api/crm/leads")
def create_crm_lead_auto(
    crm_lead_data: CRMLeadDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    background_tasks: BackgroundTasks,
):
    """
    API Endpoint: Tạo cơ hội CRM.
    """
    start_time = time.time()
    try:
        if not crm_lead_data.name or not crm_lead_data.phone or not crm_lead_data.create_uid:
            raise HTTPException(status_code=400, detail="Thiêu thông tin tạo cơ hội")

        if not fastapi:
            return [{"status": "failed", "response": "invalid fastapi"}]

        # Tạo partner
        partner = fastapi.env['crm.lead'].get_or_create_partner(
            crm_lead_data.name,
            crm_lead_data.phone,
            crm_lead_data.email
        )

        # Tạo lead
        result = fastapi.env['crm.lead'].create_crm_lead(crm_lead_data, partner)
        response_time = round(time.time() - start_time, 3)

        background_tasks.add_task(
            log_crm_api_result,
            fastapi.env,
            fastapi.id,
            fastapi._name,
            crm_lead_data.model_dump(),
            True,
            "Tạo cơ hội thành công",
            "create_crm_lead_auto",
            response_time
        )

        return {
            "status_code": 200,
            "message": "Tạo cơ hội thành công",
            "lead_ids": result.get("lead_id", [])
        }

    except UserError as e:
        # Ghi log thất bại
        response_time = round(time.time() - start_time, 3)
        background_tasks.add_task(
            log_crm_api_result,
            fastapi.env,
            fastapi.id,
            fastapi._name,
            crm_lead_data.model_dump(),
            False,
            str(e),
            "create_crm_lead_auto",
            response_time
        )

        raise HTTPException(status_code=400, detail=str(e))









