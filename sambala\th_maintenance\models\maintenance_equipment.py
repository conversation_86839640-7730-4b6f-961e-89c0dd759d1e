# -*- coding: utf-8 -*-
import json
from datetime import timedelta
from random import choice
from string import digits

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from .. import data


class MaintenanceEquipment(models.Model):
    _inherit = 'maintenance.equipment'
    _sql_constraints = [
        ('unique_th_default_code', 'unique (th_default_code)', _('This code already exists!')),
        ('unique_barcode', 'unique (th_barcode)',
         _("The Badge ID must be unique, this one is already assigned to another equipment.")),
    ]

    name = fields.Char('Equipment Name', required=True, translate=True, tracking=True)
    maintenance_team_id = fields.Many2one('maintenance.team', string='Đội bảo trì', check_company=True, tracking=True)
    warranty_date = fields.Date('Ng<PERSON>y hết hạn sản phẩm', tracking=True)
    employee_id = fields.Many2one('hr.employee', compute='_compute_equipment_assign',
                                  store=True, readonly=False, string='Nhân viên sử dụng', tracking=True)
    department_id = fields.Many2one('hr.department', compute='_compute_equipment_assign',
                                    store=True, readonly=False, string='Phòng ban sử dụng', tracking=True)
    partner_id = fields.Many2one('res.partner', string='Nhà cung cấp', check_company=True, tracking=True)
    partner_ref = fields.Char('Mã nhà cung cấp', tracking=True)
    model = fields.Char('Model', tracking=True)
    serial_no = fields.Char('Số seri', copy=False, tracking=True)
    effective_date = fields.Date('Ngày hiệu lực', default=fields.Date.context_today, required=True,
                                 help="Date at which the equipment became effective. This date will be used to compute the Mean Time Between Failure.",
                                 tracking=True)
    cost = fields.Float('Giá vốn', tracking=True)
    period = fields.Integer('Days between each preventive maintenance', tracking=True)
    maintenance_duration = fields.Float(help="Maintenance Duration in hours.", tracking=True)

    th_default_code = fields.Char('Internal Reference', copy=False, tracking=True)
    th_barcode = fields.Char(string="Barcode", help="ID used for equipment identification.",
                             copy=False)
    th_scrap_reason = fields.Char('Scrap Reason')
    th_scrap_type = fields.Selection([
        ('damaged', 'Đã hỏng'),
        ('unused', 'Vẫn có thể sử dụng nhưng không dùng đến'),
        ('not_suitable', 'Không phù hợp')
    ], string="Phân loại thanh lý")
    th_maintenance_team_id_domain = fields.Char(compute="_compute_th_maintenance_team_id_domain", readonly=True,
                                                store=False)
    th_technician_user_id_domain = fields.Char(compute="_compute_th_technician_user_id_domain", readonly=True,
                                               store=False)
    th_purchase_date = fields.Date(string="Ngày mua", tracking=True)
    # th_hand_over_date = fields.Date(string="Ngày bàn giao")
    th_recovery_date = fields.Date(string="Ngày thu hồi")
    th_price = fields.Float(string="Giá trị tài sản", tracking=True)
    th_damage_confirmed = fields.Boolean(string="Xác nhận hỏng", default=False, tracking=True)
    th_extend_confirmed = fields.Boolean(string="Xác nhận gia hạn sản phẩm", default=False, tracking=True)
    th_depreciation_period = fields.Integer(string="Thời gian khấu hao (tháng)", tracking=True)

    th_sim_type = fields.Selection([
        ('borrowed', 'SIM mượn'),
        ('used', 'SIM đã sử dụng')
    ], string="Phân loại SIM", tracking=True)
    th_scrap_unit = fields.Selection(selection=[('person', 'Cá nhân'), ('company', 'Công ty')],
                                     string="Đơn vị thanh lý")
    th_scrap_status = fields.Selection(selection=[('waiting', 'Chờ thanh lý'), ('liquidated', 'Đã thanh lý')],
                                       string="Tình trạng thanh lý")
    th_is_sim_asset = fields.Boolean(string="Là tài sản sim", default=False)
    th_equipment_states = fields.Selection(data.constants.EQUIPMENT_STATES, string="Trạng thái thiết bị", tracking=True,
                                           default='stock')
    th_sim_states = fields.Selection(data.constants.SIM_STATES, string="Trạng thái SIM", default='stock', tracking=True)

    th_specification_line_ids = fields.One2many(comodel_name='th.specification.line',
                                                inverse_name='equipment_id',
                                                string="Equipment Specifications",
                                                copy=True)
    th_sim_state_selection = fields.Many2one('th.sim.state', string="Trạng thái sim thẻ")
    th_equipment_state_selection = fields.Many2one('th.equipment.state', string="Trạng thái tài sản")
    th_location_tag_ids = fields.Many2many(comodel_name="th.location.tag", string="Khu vực")
    th_unit_used_ids = fields.Many2many(comodel_name="th.unit.used", string="Đơn vị sử dụng")
    th_currency_id = fields.Many2one('res.currency', readonly=True, default=lambda x: x.env.company.currency_id)
    th_asset_type = fields.Many2one('th.asset.type', string="Loại tài sản", related='category_id.th_asset_type_id',
                                    store=True)
    th_owner_equipment_id = fields.Many2one('th.owner.equipment', string="Quyền sở hữu", tracking=True)
    # th_department_owner_id = fields.Many2one('hr.department', 'Phòng ban sở hữu')
    th_employee_code = fields.Char(string='Mã nhân viên', compute="_compute_th_employee_code")
    th_equipment_update_ids = fields.One2many(
        'th.equipment.update',
        'th_equipment_id',
        string="Cấu hình nâng cấp"
    )
    th_has_equipment_update = fields.Boolean(
        compute="_compute_has_equipment_update",
        store=False
    )

    @api.depends('th_equipment_update_ids')
    def _compute_has_equipment_update(self):
        for rec in self:
            rec.th_has_equipment_update = bool(rec.th_equipment_update_ids)

    @api.depends('employee_id')
    def _compute_th_employee_code(self):
        for rec in self:
            rec.th_employee_code = rec.employee_id.registration_number if rec.employee_id else False

    def _log_location_changes(self, vals):
        messages = []
        if vals.get('th_location_tag_ids') and vals['th_location_tag_ids'][0][0] == 6:
            old = set(self.th_location_tag_ids.mapped('name'))
            new = set(self.env['th.location.tag'].browse(vals['th_location_tag_ids'][0][2]).mapped('name'))
            added = new - old
            removed = old - new
            if added:
                messages.append(f"✅ Thêm khu vực: {', '.join(added)}")
            if removed:
                messages.append(f"❌ Gỡ khu vực: {', '.join(removed)}")
        return messages

    def _log_unit_used_changes(self, vals):
        messages = []
        if vals.get('th_unit_used_ids') and vals['th_unit_used_ids'][0][0] == 6:
            old = set(self.th_unit_used_ids.mapped('name'))
            new = set(self.env['th.unit.used'].browse(vals['th_unit_used_ids'][0][2]).mapped('name'))
            added = new - old
            removed = old - new
            if added:
                messages.append(f"✅ Thêm đơn vị sử dụng: {', '.join(added)}")
            if removed:
                messages.append(f"❌ Gỡ đơn vị sử dụng: {', '.join(removed)}")
        return messages

    def _log_note_change(self, vals):
        messages = []
        old_note = (self.note or '').strip()
        new_note = (vals.get('note') or '').strip()
        if old_note != new_note:
            messages.append("📝 Ghi chú được thay đổi từ:")
            messages.append(f"<blockquote>{old_note[:100]}...</blockquote>")
            messages.append("sang:")
            messages.append(f"<blockquote>{new_note[:100]}...</blockquote>")
        return messages

    def _log_equipment_update_change(self, vals):
        messages = []
        ops = vals.get('th_equipment_update_ids', [])

        created = [v for op, _, v in ops if op == 0]
        updated = [(id, v) for op, id, v in ops if op == 1]
        deleted = [id for op, id, _ in ops if op == 2]

        # --- Tạo mới ---
        if created:
            lines = ["🆕 Thêm cấu hình nâng cấp:"]
            for line in created:
                spec_name = self.env['th.specification.type'].browse(line['th_specification_type']).name
                detail = line.get('th_update_detail', '')
                date = line.get('th_date_update', '')
                lines.append(f"• {spec_name} – {detail} ({date})")
            messages += lines

        # --- Cập nhật ---
        if updated:
            lines = ["✏️ Cập nhật cấu hình nâng cấp:"]
            for update_id, vals_update in updated:
                record = self.env['th.equipment.update'].browse(update_id)
                if not record.exists():
                    continue
                spec_name = record.th_specification_type.name
                detail = vals_update.get('th_update_detail', record.th_update_detail)
                date = vals_update.get('th_date_update', record.th_date_update)
                lines.append(f"• {spec_name} – {detail} ({date})")
            messages += lines

        # --- Xóa ---
        if deleted:
            lines = ["🗑️ Xóa cấu hình nâng cấp:"]
            records = self.env['th.equipment.update'].browse(deleted)
            for rec in records:
                spec_name = rec.th_specification_type.name
                lines.append(f"• {spec_name} – {rec.th_update_detail} ({rec.th_date_update})")
            messages += lines

        return messages

    def _log_specification_line_change(self, vals):
        messages = []
        ops = vals.get('th_specification_line_ids', [])

        created = [v for op, _, v in ops if op == 0]
        updated = [(id, v) for op, id, v in ops if op == 1]
        deleted = [id for op, id, _ in ops if op == 2]

        # --- Thêm mới ---
        if created:
            lines = ["🆕 Thêm thông số kỹ thuật:"]
            for line in created:
                spec = self.env['th.specification.type'].browse(line['specification_type_id']).name
                value = line.get('value', '')
                lines.append(f"• {spec}: {value}")
            messages += lines

        # --- Cập nhật ---
        if updated:
            lines = ["✏️ Cập nhật thông số kỹ thuật:"]
            for update_id, vals_update in updated:
                record = self.env['th.specification.line'].browse(update_id)
                if not record.exists():
                    continue
                spec = record.specification_type_id.name
                value = vals_update.get('value', record.value)
                lines.append(f"• {spec}: {value}")
            messages += lines

        # --- Xóa ---
        if deleted:
            lines = ["🗑️ Xóa thông số kỹ thuật:"]
            records = self.env['th.specification.line'].browse(deleted)
            for rec in records:
                lines.append(f"• {rec.specification_type_id.name}: {rec.value}")
            messages += lines

        return messages

    def _generate_specifications(self):
        for rec in self:
            if not rec.category_id:
                continue

            specs = rec.category_id.th_specifications_ids
            if not specs:
                rec.th_specification_line_ids = [(5, 0, 0)]
                continue

            rec.th_specification_line_ids = [(5, 0, 0)] + [
                (0, 0, {
                    'specification_type_id': spec.id,
                    'equipment_id': rec.id,
                    'value': '',
                }) for spec in specs
            ]

    @api.model_create_multi
    def create(self, vals_list):
        records = super(MaintenanceEquipment, self).create(vals_list)
        records._generate_specifications()
        return records

    def write(self, vals):
        for rec in self:
            messages = []
            if 'th_specification_line_ids' in vals:
                messages += self._log_specification_line_change(vals)

            if 'th_location_tag_ids' in vals:
                messages += rec._log_location_changes(vals)

            if 'th_unit_used_ids' in vals:
                messages += rec._log_unit_used_changes(vals)

            if 'note' in vals:
                messages += rec._log_note_change(vals)

            if 'th_equipment_update_ids' in vals:
                messages += rec._log_equipment_update_change(vals)

            if messages:
                rec.message_post(body="<br/>".join(messages))

        # --- Phần xử lý logic viết chung (giữ nguyên) ---
        if 'employee_id' in vals:
            employee = self.env['hr.employee'].browse(vals['employee_id'])
            if employee and employee.department_id:
                vals['department_id'] = employee.department_id.id

        if vals.get('th_equipment_states') in ['stock', 'damaged', 'canceled'] or \
                vals.get('th_sim_states') in ['stock', 'canceled']:
            vals['employee_id'] = False

        # Kiểm tra category của sim
        if 'th_is_sim_asset' in vals and vals['th_is_sim_asset']:
            sim_category = self.env['maintenance.equipment.category'].search([('name', 'ilike', 'sim thẻ')], limit=1)
            vals['category_id'] = sim_category.id if sim_category else False

        # Kiểm tra ngày thanh lý
        if 'th_scrap_status' in vals and 'scrap_date' in vals and vals['th_scrap_status'] == 'waiting':
            raise UserError("Chỉ tài sản đã thanh lý thành công mới được điền ngày thanh lý.")
        # Kiểm tra sự đối xứng giữa state và status equipment và sim
        if 'th_sim_states' in vals:
            new_state_selection = self.env['th.sim.state'].search([('th_sim_states', '=', vals['th_sim_states'])],
                                                                  limit=1)
            if new_state_selection.exists():
                vals['th_sim_state_selection'] = new_state_selection.id

        if 'th_equipment_states' in vals:
            new_state_selection = self.env['th.equipment.state'].search(
                [('th_equipment_states', '=', vals['th_equipment_states'])], limit=1)
            if new_state_selection.exists():
                vals['th_equipment_state_selection'] = new_state_selection.id

        if 'th_sim_state_selection' in vals:
            new_state = self.env['th.sim.state'].browse(vals['th_sim_state_selection'])
            if new_state.exists():
                vals['th_sim_states'] = new_state.th_sim_states

        if 'th_equipment_state_selection' in vals:
            new_state = self.env['th.equipment.state'].browse(vals['th_equipment_state_selection'])
            if new_state.exists():
                vals['th_equipment_states'] = new_state.th_equipment_states

        # Khi về trạng thái lưu kho
        if 'th_equipment_states' in vals and vals['th_equipment_states'] == 'stock':
            vals['th_recovery_date'] = fields.Date.today()

        # Khi về trạng không phải lưu kho, th_damage_confirmed phải là False
        if ('th_sim_states' in vals and vals['th_sim_states'] != 'stock') or (
                'th_equipment_states' in vals and vals['th_equipment_states'] != 'stock'):
            vals['th_damage_confirmed'] = False

        res = super(MaintenanceEquipment, self).write(vals)

        if 'category_id' in vals:
            for rec in self:
                rec._generate_specifications()

        return res

    @api.constrains('th_equipment_states', 'employee_id')
    def _check_employee_equipment_required(self):
        for record in self:
            if record.th_equipment_states == 'assigned':
                if not record.employee_id and record.equipment_assign_to == 'employee':
                    raise UserError("Bạn phải chọn nhân viên khi thiết bị được bàn giao cho nhân viên.")
                if not record.department_id and record.equipment_assign_to == 'department':
                    raise UserError("Bạn phải chọn phòng ban khi thiết bị được bàn giao cho phòng ban")
                if not record.employee_id and not record.employee_id and record.equipment_assign_to == 'other':
                    raise UserError("Bạn phải chọn phòng ban hoặc nhân viên")

    @api.constrains('th_sim_states', 'employee_id')
    def _check_employee_sim_required(self):
        for record in self:
            if record.th_sim_states == 'assigned':
                if not record.employee_id and record.equipment_assign_to == 'employee':
                    raise UserError("Bạn phải chọn nhân viên khi thiết bị được bàn giao cho nhân viên.")
                if not record.department_id and record.equipment_assign_to == 'department':
                    raise UserError("Bạn phải chọn phòng ban khi thiết bị được bàn giao cho phòng ban")
                if not record.employee_id and record.employee_id and record.equipment_assign_to == 'other':
                    raise UserError("Bạn phải chọn phòng ban hoặc nhân viên")

    @api.model
    def cron_send_maintenance_reminder(self):
        # Send mail maintenance before one month
        today = fields.Date.today()
        one_month_after = today + timedelta(days=30)

        equipments = self.search([
            ('th_equipment_states', '!=', 'canceled'),
            ('technician_user_id', '!=', False),
            ('th_extend_confirmed', '=', True),
            ('warranty_date', '=', one_month_after),
        ])

        template = self.env.ref('th_maintenance.email_template_order_confirm')

        for equip in equipments:
            if template:
                ctx = {
                    'email_from': '<EMAIL>',
                    'email_to': equip.technician_user_id.login,
                    'receiver_name': equip.technician_user_id.name,
                    'name': equip.name,
                    'date_end': equip.warranty_date,
                    'partner_id': equip.partner_id.name,
                    'cost': equip.cost
                }
                template.with_context(ctx).send_mail(equip.id, force_send=True)

    @api.model
    def default_get(self, fields_list):
        defaults = super(MaintenanceEquipment, self).default_get(fields_list)
        active_id = self.env.context.get('active_id')
        if active_id:
            defaults['th_owner_equipment_id'] = active_id
        return defaults

    @api.depends('equipment_assign_to', 'employee_id')
    def _compute_equipment_assign(self):
        for equipment in self:
            if equipment.equipment_assign_to == 'employee':
                equipment.employee_id = equipment.employee_id
                equipment.department_id = equipment.employee_id.department_id
            elif equipment.equipment_assign_to == 'department':
                equipment.employee_id = False
                equipment.department_id = equipment.department_id
            else:
                equipment.employee_id = equipment.employee_id
                equipment.department_id = equipment.employee_id.department_id
            # equipment.assign_date = fields.Date.context_today(self)

    @api.depends('category_id.maintenance_team_ids')
    def _compute_th_maintenance_team_id_domain(self):
        for rec in self:
            rec.th_maintenance_team_id_domain = json.dumps(
                [('id', 'in', rec.category_id.maintenance_team_ids.ids)]
            )

    @api.depends('maintenance_team_id.member_ids')
    def _compute_th_technician_user_id_domain(self):
        for rec in self:
            rec.th_technician_user_id_domain = json.dumps(
                [('id', 'in', rec.maintenance_team_id.member_ids.ids)]
            )

    @api.onchange('th_equipment_states')
    def _check_th_equipment(self):
        if self.th_equipment_states == 'disposed' or self.th_equipment_states == 'pending_disposal':
            raise UserError("Chuyển về tồn kho và sử dụng nút thanh lý")

    @api.onchange('th_equipment_state_selection')
    def _onchange_th_equipment_state_selection(self):

        if self.th_equipment_state_selection.th_equipment_states == 'disposed' or self.th_equipment_state_selection.th_equipment_states == 'pending_disposal':
            raise UserError("Chuyển về tồn kho và sử dụng nút thanh lý")

    @api.onchange('th_is_sim_asset')
    def _onchange_th_is_sim_asset(self):
        if not self.th_is_sim_asset:
            if self.category_id or self.th_sim_state_selection:
                self.update({
                    'category_id': False,
                    'th_sim_state_selection': False,
                    'th_damage_confirmed': False
                })
            else:
                self.th_damage_confirmed = False
            return

        values_to_update = {'th_damage_confirmed': False}

        if self.th_equipment_state_selection:
            values_to_update['th_equipment_state_selection'] = False

        self.update(values_to_update)

    @api.onchange('category_id')
    def _onchange_category_id(self):
        self.update({
            'maintenance_team_id': False,
        })

        if self.category_id:
            sim_category = self.env['maintenance.equipment.category'].search([('name', 'ilike', 'sim thẻ')], limit=1)
            is_sim = self.category_id.id == sim_category.id if sim_category else False

    @api.onchange('maintenance_team_id')
    def _onchange_maintenance_team_id(self):
        self.technician_user_id = False

    @api.onchange('th_extend_confirmed')
    def _onchange_clear_warranty_date(self):
        if not self.th_extend_confirmed:
            self.warranty_date = False

    def action_get_equipment_by_barcode(self, barcode_no):
        equipment_id = self.env['maintenance.equipment'].sudo().search([('th_barcode', '=', barcode_no)], limit=1).id
        return equipment_id

    def action_open_scrap_popup(self):
        return {
            'name': _('Scrap Equipment'),
            'type': 'ir.actions.act_window',
            'res_model': 'maintenance.equipment',
            'view_id': self.env.ref('th_maintenance.scrap_equipment_view_form').id,
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
        }

    def action_scrap(self):
        for record in self:
            record.active = False
            # Cập nhật th_equipment_states theo th_scrap_status
            if record.th_scrap_status == 'liquidated':
                record.th_equipment_states = 'disposed'
            if record.th_scrap_status == 'waiting':
                record.th_equipment_states = 'pending_disposal'

    def generate_random_barcode(self):
        for equipment in self:
            equipment.th_barcode = '057' + "".join(choice(digits) for i in range(9))
