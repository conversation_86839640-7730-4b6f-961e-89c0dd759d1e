<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--form c<PERSON> hội đang trùng kế thừa form c<PERSON> hội vstep-->
    <record id="th_apm_vstep_duplicate_inherit_form_view" model="ir.ui.view">
        <field name="name">th.apm.vstep.duplicate.form</field>
        <field name="inherit_id" ref="th_apm_vstep.th_apm_vstep_lead_form_view"/>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <!-- Thêm nút khiếu nại vào header -->
            <xpath expr="//header" position="inside">
                <button name="th_action_complaint" string="Khiếu nại" type="object"
                        class="oe_highlight"
                        attrs="{'invisible': ['|', ('th_is_a_duplicate_opportunity', '=', False), ('th_is_under_complaint', '=', True)]}"/>
            </xpath>
            <xpath expr="//button[@name='action_open_apm_partner']" position="after">
                <button name="action_view_history" type="object" class="oe_stat_button"
                                icon="fa-address-card"
                                attrs="{'invisible': ['|', ('th_is_a_duplicate_opportunity', '=', False), ('th_is_under_complaint', '=', True)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Lịch sử chăm sóc
                                </span>
                            </div>
                        </button>
            </xpath>

            <xpath expr="//widget[@name='web_ribbon']" position="after">
                <!-- <widget name="web_ribbon" title="Đang khiếu nại" bg_color="bg-warning" invisible="not context.get('th_duplicate_type', False)"/> -->
                <widget name="web_ribbon" title="Đang khiếu nại" bg_color="bg-warning" attrs="{'invisible': ['|','|',('th_is_a_duplicate_opportunity', '=', False),('th_is_under_complaint', '=', False),('th_reason', '!=', False)]}"/>
                <widget name="web_ribbon" title="Cơ hội trùng" bg_color="bg-danger" attrs="{'invisible': ['|','|',('th_is_a_duplicate_opportunity', '=', False),('th_is_under_complaint', '=', True),('th_reason', '!=', False)]}"/>
                <widget name="web_ribbon" title="Cơ hội đóng" bg_color="bg-danger" attrs="{'invisible': [('th_is_close_lead', '=', False)]}"/>
            </xpath>
            <field name="th_is_a_duplicate_opportunity" position="after">
                <field name="th_dup_need_admin" invisible="1"/>
                <field name="th_is_under_complaint" invisible="1"/>
                <field name="th_is_close_lead" invisible="1"/>
            </field>
        </field>
    </record>

    <!--form khiếu nại cơ hội trùng kế thừa form cơ hội vstep-->
    <record id="th_apm_vstep_complaint_duplicate_view_form" model="ir.ui.view">
        <field name="name">th.apm.vstep.complaint.duplicate.view.form</field>
        <field name="inherit_id" ref="th_apm_vstep.th_apm_vstep_lead_form_view"/>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <!-- Thêm nút khiếu nại vào header -->
            <xpath expr="//header" position="inside">
                <button name="th_action_list_duplicate" string="Danh sách lead trùng" type="object"
                        class="oe_highlight no-wrap" invisible="not context.get('th_duplicate_type', False)"/>
                <button name="th_action_keep_opportunity" string="Giữ cơ hội" type="object" class="oe_highlight"
                        invisible="not context.get('th_duplicate_type', False)"/>
                <!-- <button name="th_action_close_opportunity" string="Đóng cơ hội" type="object" class="btn-secondary" invisible="not context.get('th_duplicate_type', False)"/>
                <button name="th_action_open_opportunity" string="Mở cơ hội" type="object" class="btn-secondary" invisible="not context.get('th_duplicate_type', False)"/> -->
            </xpath>
            <field name="th_is_a_duplicate_opportunity" position="after">
                <field name="th_duplicate_type" invisible="1"/>
                <field name="th_is_under_complaint" invisible="1"/>
                <field name="th_is_close_lead" invisible="1"/>
            </field>
        </field>
    </record>

    <record id="th_apm_vstep_lead_duplicate_act" model="ir.actions.server">
        <field name="name">Cơ hội đang trùng</field>
        <field name="model_id" ref="th_apm.model_th_apm"/>
        <field name="state">code</field>
        <field name="code">action = model.th_action_view_apm_vstep_lead_duplicate()</field>
    </record>

    <record id="th_apm_vstep_duplicate_check_history_acti" model="ir.actions.server">
        <field name="name">Lịch sử kiểm tra trùng</field>
        <field name="model_id" ref="th_apm.model_th_apm"/>
        <field name="state">code</field>
        <field name="code">action = model.th_action_view_apm_vstep_duplicate_history()</field>
    </record>

    <record id="th_apm_vstep_complaint_duplicate_act" model="ir.actions.server">
        <field name="name">Trùng cơ hội</field>
        <field name="model_id" ref="th_apm.model_th_apm"/>
        <field name="state">code</field>
        <field name="code">action = model.th_apm_vstep_complaint_duplicate_action()</field>
    </record>
</odoo>
