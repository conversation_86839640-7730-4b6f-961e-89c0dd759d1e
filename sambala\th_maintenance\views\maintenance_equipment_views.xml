<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--Inherit and modify Equipment Form View-->
        <record id="th_maintenance.hr_equipment_view_form_inherit" model="ir.ui.view">
            <field name="model">maintenance.equipment</field>
            <field name="inherit_id" ref="maintenance.hr_equipment_view_form"/>
            <field name="arch" type="xml">
                <!--Ẩn button với người dùng không có quyền-->
                <xpath expr="//button[@name='%(maintenance.hr_equipment_request_action_from_equipment)d']"
                       position="attributes">
                    <attribute name="groups">th_maintenance.group_equipment_leader,maintenance.group_equipment_manager
                    </attribute>
                </xpath>

                <xpath expr="/form/*" position="before">
                    <header>
                        <field name="active" invisible="True"/>
                        <button name="action_open_scrap_popup" string="Thanh lý" type="object" class="btn-primary"
                                attrs="{'invisible': ['|', ('th_is_sim_asset', '=', True), ('th_equipment_states', 'in', ['borrowed', 'assigned', 'damaged', 'canceled', 'disposed'])]}"
                        />
                        <field name="th_equipment_states" widget="statusbar"
                               attrs="{'invisible': ['|', '|',('th_damage_confirmed', '=', True), ('th_is_sim_asset', '=', True), ('th_equipment_states', 'in', ['disposed', 'pending_disposal'])]}"
                               options="{'clickable': '1'}"/>
                        <field name="th_equipment_states" widget="statusbar"
                               attrs="{'invisible': ['|', '|',('th_damage_confirmed', '=', False), ('th_is_sim_asset', '=', True), ('th_equipment_states', 'in', ['borrowed', 'assigned', 'damaged', 'canceled'])]}"/>
                        <field name="th_equipment_states" widget="statusbar"
                               attrs="{'invisible': [('th_scrap_status', '=', False)]}"/>
                        <field name="th_sim_states" widget="statusbar"
                               attrs="{'invisible': [('th_is_sim_asset', '!=', True)]}"
                               options="{'clickable': '1'}"/>
                    </header>
                </xpath>

                <xpath expr="/form/sheet/widget[@name='web_ribbon']" position="replace">
                    <div role="alert" class="alert alert-warning text-center"
                         attrs="{'invisible': [('active', '=', True)]}">
                        <field name="th_currency_id" invisible="1"/>
                        <div>
                            <label for="scrap_date" string="Ngày thanh lý: "/>
                            <field name="scrap_date" nolabel="1" readonly="1"/>
                        </div>
                        <div>
                            <label for="th_scrap_reason" string="Reason: "/>
                            <field name="th_scrap_reason" nolabel="1" readonly="1"/>
                        </div>
                        <div>
                            <label for="th_scrap_type" string="Loại tài sản thanh lý: "/>
                            <field name="th_scrap_type" nolabel="1" readonly="1"/>
                        </div>
                        <div>
                            <label for="th_price" string="Đơn giá: "/>
                            <field name="th_price" widget="monetary" options="{'currency_field': 'th_currency_id'}"
                                   nolabel="1" readonly="1"/>
                        </div>
                        <div>
                            <label for="th_scrap_unit" string="Đơn vị thanh lý: "/>
                            <field name="th_scrap_unit" nolabel="1" readonly="1"/>
                        </div>
                        <div>
                            <!--                            <label for="th_scrap_status" string="Trạng thái thanh lý: "/>-->
                            <field name="th_scrap_status" nolabel="1" readonly="1" invisible="1"/>
                        </div>
                    </div>
                </xpath>

                <xpath expr="//page[@name='maintenance']" position="after">
                    <page string="Lịch sử nâng cấp" name="equipment_update_history">
                        <field name="th_equipment_update_ids" context="{'default_th_equipment_id': active_id}">
                            <tree string="Lịch sử nâng cấp" editable="bottom">
                                <field name="th_specification_type"/>
                                <field name="th_update_detail"/>
                                <field name="th_date_update"/>
                                <field name="th_equipment_spec_ids" invisible="1"/>
                            </tree>
                        </field>
                    </page>
                </xpath>

                <xpath expr="//page[@name='maintenance']" position="after">
                    <!--Add Equipment Specification Page-->
                    <page string="Equipment Specifications" name="equipment_specifications">
                        <field name="th_specification_line_ids">
                            <tree editable="bottom" delete="false" create="false">
                                <field name="specification_type_id"
                                       options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                                       required="True"/>
                                <field name="value" required="True" options="{'no_open': True}"/>
                            </tree>
                        </field>
                    </page>

                </xpath>


                <xpath expr="//div[@class='oe_title']" position="replace">
                    <h1>
                        <div class="o_row">
                            <label for="th_default_code" string="Mã tài sản"
                                   class="o_form_label"
                                   attrs="{'invisible': [('th_is_sim_asset', '=', True)]}"/>
                            <label for="th_default_code" string="Mã nội bộ"
                                   class="o_form_label"
                                   attrs="{'invisible': [('th_is_sim_asset', '!=', True)]}"/>
                            <field name="th_default_code" required="True" class="o_form_field"/>
                        </div>
                    </h1>
                    <div class="o_row">
                        <label for="name" class="o_form_label"/>
                        <field name="name" string="Tên tài sản" class="o_form_field"/>
                    </div>
                </xpath>


                <xpath expr="//form/sheet/group/group[field[@name='active']]" position="replace">
                    <group>
                        <field name="th_is_sim_asset"
                               attrs="{'invisible': ['|', '|' , ('th_equipment_states', 'in', ['pending_disposal', 'disposed', 'assigned']), ('th_sim_states', '=', 'assigned'), ('th_equipment_update_ids', '!=', [])]}"/>
                        <field name="th_barcode" attrs="{'invisible': [('th_is_sim_asset', '!=', True)]}"/>
                        <field name="active" invisible="1"/>
                        <field name="th_has_equipment_update" invisible="1"/>
                        <field name="category_id" options="{'no_open': True}"
                               force_save="1"
                               context="{'default_company_id': company_id}"
                               attrs="{'readonly': ['|', ('th_is_sim_asset', '=', True), ('th_equipment_update_ids', '!=', [])]}"/>
                        />
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"
                               invisible="1"/>
                        <field name="equipment_assign_to" string="Được dùng bởi" widget="radio"
                               class="hide-radio-options"/>
                        <field name="employee_id" string="Nhân viên sử dụng"
                               attrs="{'invisible': ['|', '|', ('equipment_assign_to', '=', 'department'),
                                     ('equipment_assign_to', '=', False),
                                     ('th_is_sim_asset', '=', True)]}"/>
                        <field name="th_employee_code" attrs="{'invisible': [('employee_id', '=', False)]}" groups="hr.group_hr_user"/>
                        <field name="department_id" string="Phòng ban sử dụng"
                               attrs="{
           'invisible': ['|', ('equipment_assign_to', '=', False), ('th_is_sim_asset', '=', True)],
           'readonly': [('equipment_assign_to', '=', 'employee')]
       }"/>
                        <field name="th_sim_state_selection" attrs="{'invisible': [('th_is_sim_asset', '!=', True)]}"/>
                        <field name="th_equipment_state_selection"
                               attrs="{'readonly': ['|', ('th_damage_confirmed', '=', True), ('th_equipment_states', 'in', ['disposed', 'pending_disposal'])], 'invisible': [('th_is_sim_asset', '=', True)]}"/>
                    </group>
                </xpath>

                <xpath expr="//field[@name='maintenance_team_id']" position="replace">
                    <field name="th_damage_confirmed"
                           attrs="{'readonly': [('active', '=', False)],
                                   'invisible': ['|',
                                       '&amp;', ('th_sim_states', '!=', 'stock'), ('th_is_sim_asset', '=', True),
                                       '&amp;', ('th_equipment_states', '!=', 'stock'), ('th_is_sim_asset', '=', False)
                                   ]}"/>
                    <field name="th_maintenance_team_id_domain" invisible="1"/>
                    <field name="maintenance_team_id" domain="th_maintenance_team_id_domain"
                           attrs="{'invisible': [('th_is_sim_asset', '=', True)]}"/>
                    <field name="th_owner_equipment_id"/>
                    <field name="th_asset_type" attrs="{'invisible': [('th_is_sim_asset', '=', True)]}"
                           options="{'no_open': True}"/>
                </xpath>

                <xpath expr="//field[@name='technician_user_id']" position="replace">
                    <field name="th_technician_user_id_domain" invisible="1"/>
                    <field name="technician_user_id" domain="th_technician_user_id_domain"
                           attrs="{'invisible': [('th_is_sim_asset', '=', True)]}"/>
                    <field name="th_purchase_date" attrs="{'invisible': [('th_is_sim_asset', '=', True)]}"/>
                </xpath>

                <xpath expr="/form/sheet/group/group/field[@name='scrap_date']" position="replace">
                </xpath>

                <xpath expr="//field[@name='assign_date']" position="attributes">
                    <attribute name="groups"></attribute>
                    <attribute name="string">Ngày bàn giao</attribute>
                </xpath>

                <xpath expr="//field[@name='location']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//field[@name='location']" position="after">
                    <field name="employee_id" string="Nhân viên sử dụng"
                           attrs="{'invisible': ['|', '|', ('equipment_assign_to', '=', 'department'),
                                     ('equipment_assign_to', '=', False),
                                     ('th_is_sim_asset', '!=', True)]}"/>

                    <field name="department_id" string="Phòng ban sử dụng"
                           attrs="{'invisible': ['|', ('equipment_assign_to', '=', False), ('th_is_sim_asset', '!=', True)], 'readonly': [('equipment_assign_to', '=', 'employee')]}"
                    />
                    <field name="th_sim_type" attrs="{'invisible': [('th_is_sim_asset', '!=', True)]}"/>
                    <field name="th_price" attrs="{'invisible': [('th_is_sim_asset', '=', True)]}"/>
                    <field name="th_depreciation_period" attrs="{'invisible': [('th_is_sim_asset', '=', True)]}"/>
                    <field name="th_location_tag_ids" widget="many2many_tags"
                           options="{'color_field': 'color', 'no_create_edit': True}"/>
                    <field name="th_unit_used_ids" widget="many2many_tags"
                           options="{'color_field': 'color', 'no_create_edit': True}"
                           attrs="{'invisible': [('th_is_sim_asset', '=', True)]}"/>
                </xpath>

                <xpath expr="//page[@name='product_information']//field[@name='partner_id']" position="attributes">
                    <attribute name="context">{'form_view_ref' : 'th_maintenance.th_maintenance_view_partner_form',
                        'default_company_type': 'company'}
                    </attribute>
                    <attribute name="domain">[('company_id', 'in', [company_id, False]), ('is_company', '=', True)]
                    </attribute>
                    <!--                    <attribute name="domain">[('company_type', '=', 'company')]</attribute>-->
                </xpath>
                <xpath expr="//page[@name='product_information']//field[@name='warranty_date']" position="replace">
                    <field name="warranty_date" attrs="{'invisible': [('th_extend_confirmed', '=', False)]}"/>
                    <field name="th_extend_confirmed"/>
                </xpath>

                <xpath expr="//page[@name='maintenance']" position="attributes">
                    <attribute name="attrs">{'invisible': [('th_is_sim_asset', '=', True)]}</attribute>
                </xpath>

            </field>
        </record>
        <!--Inherit and modify Equipment Search View-->
        <record id="hr_equipment_view_search_inherit" model="ir.ui.view">
            <field name="model">maintenance.equipment</field>
            <field name="inherit_id" ref="maintenance.hr_equipment_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='my']" position="replace">
                    <filter string="My Equipments" name="my"
                            domain="['|', ('employee_id', '=', uid), ('department_id.manager_id.user_id', '=', uid)]"/>
                    <filter name="group_by_asset_type" string="Nhóm theo Loại tài sản"
                            context="{'group_by': 'th_asset_type'}"/>
                </xpath>

                <xpath expr="//filter[@name='assigned']" position="replace">
                    <filter string="Assigned" name="assigned"
                            domain="['|', ('employee_id', '!=', False), ('department_id', '!=', False)]"/>
                </xpath>

                <xpath expr="//filter[@name='available']" position="replace">
                    <filter string="Unassigned" name="available"
                            domain="[('employee_id', '=', False), ('department_id', '=', False)]"/>
                </xpath>
                <xpath expr="//filter[@name='inactive']" position="replace">
                    <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
                </xpath>

                <xpath expr="//filter[@name='message_needaction']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//filter[@name='technicians']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//filter[@name='category']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//filter[@name='owner']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//filter[@name='vendor']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//filter[@name='employee']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//filter[@name='department']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//filter[@name='available']" position="after">
                    <group expand="0" string="Nhóm theo...">
                        <filter string="Trạng thái SIM thẻ" name="group_by_sim_selection"
                                context="{'group_by': 'th_sim_state_selection'}"/>
                        <filter string="Trạng thái tài sản" name="group_by_equipment_selection"
                                context="{'group_by': 'th_equipment_state_selection'}"/>
                        <filter string="Trạng thái thiết bị" name="group_by_equipment_states"
                                context="{'group_by': 'th_equipment_states'}"/>
                        <filter string="Trạng thái SIM" name="group_by_sim_states"
                                context="{'group_by': 'th_sim_states'}"/>
                    </group>
                </xpath>

            </field>
        </record>

        <!--Scrap Equipment-->
        <record id="scrap_equipment_view_form" model="ir.ui.view">
            <field name="name">Scrap Equipment</field>
            <field name="model">maintenance.equipment</field>
            <field name="priority">1000</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="scrap_date"
                                   attrs="{'readonly': [('active', '=', False), ('scrap_date', '!=', False)]}"
                                   force_save="1"/>
                            <field name="th_scrap_reason"
                                   attrs="{'readonly': [('active', '=', False), ('th_scrap_reason', '!=', False)]}"
                                   force_save="1"/>
                            <field name="th_scrap_type" required="1"
                                   attrs="{'readonly': [('active', '=', False), ('th_scrap_type', '!=', False)]}"
                                   force_save="1"/>
                            <field name="th_price" widget="monetary" options="{'currency_field': 'th_currency_id'}"
                                   required="1"
                                   force_save="1"/>
                            <field name="th_scrap_unit" required="1"
                                   attrs="{'readonly': [('active', '=', False), ('th_scrap_unit', '!=', False)]}"
                                   force_save="1"/>
                            <field name="th_scrap_status" required="1"
                                   attrs="{'readonly': [('active', '=', False), ('th_scrap_status', '=', 'liquidated')]}"
                                   force_save="1"/>
                            <field name="th_currency_id" invisible="1"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <footer>
                            <button name="action_scrap" type="object" string="Scrap" class="btn-primary"/>
                            <button string="Cancel" class="btn-secondary" special="cancel"/>
                        </footer>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_maintenance_equipment_kanban_custom_group" model="ir.ui.view">
            <field name="name">maintenance.equipment.kanban.custom</field>
            <field name="model">maintenance.equipment</field>
            <field name="inherit_id" ref="maintenance.hr_equipment_view_kanban"/>
            <field name="arch" type="xml">
                <xpath expr="//templates" position="replace">
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="o_kanban_record_top">
                                        <b class="o_kanban_record_title">
                                            <field name="th_default_code"/>
                                        </b>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div>
                                            <i class="fa fa-user me-1"/>
                                            <field name="employee_id"/>
                                        </div>
                                        <div>
                                            <i class="fa fa-building me-1"/>
                                            <field name="department_id"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_bottom mt-2 d-flex justify-content-between align-items-end">
                                        <div class="o_kanban_bottom_left"/>
                                        <div class="o_kanban_bottom_right">
                                            <field name="th_location_tag_ids" widget="many2many_tags"
                                                   options="{'color_field': 'color'}"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="clearfix"/>
                            </div>
                        </t>
                    </templates>
                </xpath>
            </field>
        </record>


        <record id="inherit_hr_equipment_tree" model="ir.ui.view">
            <field name="name">equipment.tree.override</field>
            <field name="model">maintenance.equipment</field>
            <field name="inherit_id" ref="maintenance.hr_equipment_view_tree"/>
            <field name="arch" type="xml">
                <xpath expr="/tree" position="replace">
                    <tree string="Danh sách thiết bị">
                        <field name="th_default_code" string="Mã tài sản"/>
                        <field name="name"/>
                        <field name="employee_id" string="Nhân viên sử dụng"/>
                        <field name="department_id" string="Phòng ban sử dụng"/>
                        <field name="th_location_tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                        <field name="category_id"/>
                    </tree>
                </xpath>
            </field>
        </record>
    </data>
</odoo>