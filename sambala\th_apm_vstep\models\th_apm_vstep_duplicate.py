from odoo import (api, fields, models)


class ThApmVstepDuplicate(models.Model):
    _inherit = 'th.apm'

    # view c<PERSON> hội đang trùng theo vstep
    @api.model
    def th_action_view_apm_vstep_lead_duplicate(self):
        vstep_origin = self.env.ref('th_setup_parameters.th_origin_vstep')
        return {
            'name': 'Cơ hội đang trùng',
            'type': 'ir.actions.act_window',
            'res_model': 'th.apm',
            'view_mode': 'tree,form',
            'domain': [('th_origin_id', '=', vstep_origin.id), ('th_is_a_duplicate_opportunity', '=', True)],
            'context': {'create': False},
            'views': [
                (self.env.ref("th_apm.th_apm_duplicate_tree_view").id, 'tree'),
                (self.env.ref("th_apm_vstep.th_apm_vstep_duplicate_inherit_form_view").id, 'form'),
            ],
            'search_view_id': [self.env.ref("th_apm.th_apm_duplicate_search_view").id],
        }

    def action_view_history(self):
        self.ensure_one()
        apm_id = self.sudo().search([('th_partner_id', '=', self.th_partner_id.id), ('th_origin_id', '=', self.th_origin_id.id),
                                     ('id', '!=', self.id), ('th_is_a_duplicate_opportunity', '=', False)], order='id asc')
        if len(apm_id) > 1:
            apm_id = self.env['th.apm'].sudo().search(
                [('id', '!=', self.id), ('th_origin_id', '=', self.th_origin_id.id),
                 ('th_partner_id', '=', self.th_partner_id.id), ('th_is_a_duplicate_opportunity', '=', False),
                 ('th_is_close_lead', '=', False)])
            if not apm_id:
                apm_id = self.env['crm.lead'].sudo().search(
                    [('id', '!=', self.id), ('th_origin_id', '=', self.th_origin_id.id),
                     ('th_partner_id', '=', self.th_partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)], limit=1)
        for apm in apm_id:
            if not self.env['th.care.history.vstep'].search([('th_apm_lead_old_id', '=', apm.id), ('th_apm_lead_new_id', '=', self.id)]):
                self.env['th.care.history.vstep'].create({'th_apm_lead_old_id': apm.id,
                                                    'th_apm_lead_new_id': self.id})
        return {
            'name': 'Lịch sử chăm sóc',
            'view_mode': 'tree,form',
            'res_model': "th.care.history.vstep",
            'type': 'ir.actions.act_window',
            'target': 'target',
            'domain': [('th_apm_lead_new_id', '=', self.id)],
        }

    # view cơ hội trùng khiếu nại theo vstep
    @api.model
    def th_apm_vstep_complaint_duplicate_action(self):
        vstep_origin = self.env.ref('th_setup_parameters.th_origin_vstep')
        return {
            'name': 'Khiếu nại cơ hội trùng',
            'type': 'ir.actions.act_window',
            'res_model': 'th.apm',
            'view_mode': 'tree,form',
            'domain': [('th_origin_id', '=', vstep_origin.id), ('th_is_under_complaint', '=', True),('th_is_a_duplicate_opportunity', '=', False)],
            'context': {'th_duplicate_type':'need_handle','create': False},
            'views': [
                (self.env.ref("th_apm_vstep.th_apm_vstep_lead_tree_view").id, 'tree'),
                (self.env.ref("th_apm_vstep.th_apm_vstep_complaint_duplicate_view_form").id, 'form'),
            ],
        }

    # view lịch sử kiểm tra trùng theo vstep
    @api.model
    def th_action_view_apm_vstep_duplicate_history(self):
        vstep_origin = self.env.ref('th_setup_parameters.th_origin_vstep')
        return {
            'name': 'Lịch sử kiểm tra trùng',
            'type': 'ir.actions.act_window',
            'res_model': 'th.apm.duplicate.check.history',
            'view_mode': 'tree,form',
            'domain': [('th_origin_id', '=', vstep_origin.id)],
            'context': {'create': False},
        }


