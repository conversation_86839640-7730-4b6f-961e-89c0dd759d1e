# -*- coding: utf-8 -*-
from odoo import models, fields, api, tools, _
from odoo.exceptions import ValidationError, UserError


class ThFeedbackTicket(models.Model):
    _name = "th.feedback.ticket"
    _description = "Phiếu feedback"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _order = 'id desc'

    name = fields.Char(string="Tên", required=True)
    color = fields.Integer(string="Màu sắc")
    th_description = fields.Html(string="Mô tả", tracking=True)
    th_partner_id = fields.Many2one("res.partner", string="<PERSON>h<PERSON><PERSON> hàng", required=True)
    th_partner_ids = fields.Many2many("res.partner", string="Người tạo lỗi/feedback", readonly=True)
    th_email = fields.Char(string="Email")
    th_phone = fields.Char(string="Số điện thoại")
    th_deadline = fields.Date(string="Deadline đề xuất")
    uid_create = fields.Many2one("res.users", string="Người tạo phiếu", default=lambda self: self.env.user, readonly=True)
    th_attachment_ids = fields.Many2many("ir.attachment", string="Tệp đính kèm")
    priority = fields.Selection([("0", "Góp ý"),
                                 ("1", "Bình thường"),
                                 ("2", "Gấp"),
                                 ("3", "Khẩn cấp")],
                                string="Độ ưu tiên",
                                default="0")
    th_company_id = fields.Many2one("res.company", string="Công ty")
    th_category_id = fields.Many2one("th.feedback.category", string="Danh mục feedback", required=True)
    th_topic_id = fields.Many2one("th.feedback.topic",string="Chủ đề",domain="[('th_category_id', '=', th_category_id)]",required=True,)
    th_team_id = fields.Many2one("th.feedback.team", string="Đội", domain = "[('id', 'in', th_team_ids)]")
    th_assign_id = fields.Many2one("res.users", string="Phân công cho", tracking=True, domain="[('id', 'in', th_user_auto_assign_ids)]")
    th_deadline_expected = fields.Date(string="Deadline dự kiến")
    th_partner_code = fields.Many2one("th.feedback.partner", string="Mã đối tác")
    th_product_id = fields.Many2one("product.template", string="Tên học liệu")
    th_product_code = fields.Char(string="Mã học liệu", related="th_product_id.default_code", store=True)
    th_component_position = fields.Many2one("th.feedback.position", string="Vị trí thành phần")
    th_question_position = fields.Char(string="Vị trí bài/câu")
    th_error_id = fields.Many2one("th.feedback.error", string="Phân loại lỗi")
    is_no_error = fields.Boolean(string="Không phải lỗi", related="th_error_id.is_no_error")
    th_error_detail_id = fields.Many2one("th.feedback.error.detail",string="Đặc điểm lỗi",domain="[('th_error_id', '=', th_error_id)]",)
    # description when error is "Không có lỗi" and "Lỗi từ sinh viên"
    th_description_error = fields.Text(string="Mô tả lỗi")
    th_stage_id = fields.Many2one("th.feedback.stage", string="Giai đoạn", tracking=True, group_expand='_expand_stages')
    th_ticket_stage = fields.Selection(string="Giai đoạn", related="th_stage_id.th_ticket_stage")
    th_transfer_ids = fields.One2many("th.feedback.transfer", "th_ticket_id", string="Phiếu chuyển phiếu feedback", tracking=True)
    kanban_state = fields.Selection([("normal", "Grey"), ("done", "Green"), ("blocked", "Red")], string="Trạng thái Kanban", default="normal", tracking=True)
    th_user_auto_assign_ids = fields.Many2many("res.users", string="Người tự động gán", related="th_team_id.th_user_auto_assign_ids")
    th_team_ids = fields.Many2many("th.feedback.team", string="Đội", compute="_compute_get_team")
    th_sla_policy_id = fields.Many2one("th.sla.policy", string="Thời gian xử lý tối đa (SLA)")
    th_check_group_feedback_classifier = fields.Boolean(compute='th_compute_check_group_feedback_classifier',
                                                        string="Check nhân viên phân loại lỗi")

    @api.depends('th_topic_id', 'th_category_id')
    def _compute_get_team(self):
        # Tính toán một lần cho tất cả các bản ghi
        records_with_topic = self.filtered(lambda r: r.th_category_id and r.th_topic_id)
        records_with_category = self.filtered(lambda r: r.th_category_id and not r.th_topic_id)
        if records_with_topic or records_with_category:
            # Xử lý các bản ghi có cả category và topic
            if records_with_topic:
                for record in records_with_topic:
                    record.th_team_ids = record.th_topic_id.th_team_ids
            # Xử lý các bản ghi chỉ có category
            if records_with_category:
                for record in records_with_category:
                    record.th_team_ids = record.th_category_id.th_topic_ids.mapped('th_team_ids')
        else:
            # Xử lý các bản ghi còn lại
            for record in self - records_with_topic - records_with_category:
                record.th_team_ids = False

    def _expand_stages(self, stages, domain, order):
        stage_ids = self.env['th.feedback.stage'].search([])
        return stage_ids

    # xử lý logic đổi stage phiếu
    @api.onchange("th_stage_id")
    def onchange_th_stage_id(self):
        # Kiểm tra nếu là tạo mới hoặc stage thực sự thay đổi
        if self._origin and self._origin.th_stage_id != self.th_stage_id:
            # Quyền kéo trạng thái:
            # - Người dùng chỉ có thể thay đổi giai đoạn phiếu sang trạng thái Mới và Đã hủy.
            # - Nhân viên xử lý lỗi chỉ có thể thay đổi giai đoạn phiếu sang trạng thái Xử lý, Chờ phản hồi và Đã xử lý.
            # - Quản lý, Quản trị viên và Người tạo phiếu được thay đổi giai đoạn phiếu sang trạng thái Đóng.
            if ((not self.env.user.has_group(
                    'th_feedback.group_feedback_handler') and self.th_stage_id.th_ticket_stage not in ('new', 'cancel', 'close'))
                    or (not self.env.user.has_group(
                        'th_feedback.group_feedback_classifier') and self.th_stage_id.th_ticket_stage == 'error_classify')
                    or (not self.env.user.has_group(
                        'th_feedback.group_feedback_category_manager') and self.th_stage_id.th_ticket_stage == 'close' and not self.create_uid == self.env.user)):
                raise UserError("Bạn không có quyền thay đổi giai đoạn này của phiếu!")

            # If user has permission, proceed with other validations
            if self.th_stage_id:
                if self.th_stage_id.th_ticket_stage in ['error_classify', 'new']:
                    self.th_error_id = False
                    self.th_error_detail_id = False
                elif self.th_stage_id.th_ticket_stage not in ['error_classify', 'cancel', 'new'] and not self.th_error_id:
                    raise ValidationError("Không thể đổi giai đoạn khi chưa phân loại lỗi")
            
    @api.onchange("th_feedback_topic_id")
    def onchange_th_feedback_topic_id(self):
        for record in self:
            if record.th_feedback_topic_id and record.th_feedback_topic_id.team_ids:
                record.team_ids = record.th_feedback_topic_id.team_ids

    @api.onchange("th_partner_id")
    def onchange_th_partner_id(self):
        if self.th_partner_id:
            self.th_email = self.th_partner_id.email
            self.th_phone = self.th_partner_id.phone

    @api.onchange("th_email")
    def _check_email(self):
        for record in self:
            if record.th_email and not tools.single_email_re.match(record.th_email):
                raise ValidationError("Email không đúng định dạng")

    @api.onchange("th_phone")
    def _check_phone(self):
        for record in self:
            if record.th_phone and not record.th_phone.isdigit():
                raise ValidationError("Số điện thoại chỉ được chứa các chữ số")

    @api.onchange("th_deadline", "th_deadline_expected")
    def _check_dates(self):
        for record in self:
            if record.th_deadline and record.th_deadline_expected:
                if record.th_deadline > record.th_deadline_expected:
                    raise ValidationError(
                        "Deadline đề xuất không thể sau deadline dự kiến"
                    )

    @api.constrains("th_attachment_ids")
    def _check_attachments(self):
        for record in self:
            for attachment in record.th_attachment_ids:
                if attachment.file_size > 25 * 1024 * 1024:  # 25MB
                    raise ValidationError("Kích thước file không được vượt quá 25MB")
                allowed_extensions = [
                    ".pdf",
                    ".doc",
                    ".docx",
                    ".xls",
                    ".xlsx",
                    ".jpg",
                    ".png",
                ]
                if not any(
                    attachment.name.lower().endswith(ext) for ext in allowed_extensions
                ):
                    raise ValidationError("Định dạng file không được hỗ trợ")

    
    @api.model
    def create(self, vals_list):
        defaults = super().create(vals_list)
        # Lấy stage đầu tiên theo thứ tự sequence
        first_stage = self.env['th.feedback.stage'].search([], order='sequence', limit=1)
        if first_stage:
            defaults['th_stage_id'] = first_stage.id
        if vals_list.get("th_stage_id"):
            defaults['th_stage_id'] = vals_list.get("th_stage_id")

        return defaults

    # xử lý logic auto assign
    @api.onchange("th_team_id")
    def _onchange_auto_assign(self):
        if self.th_team_id.th_auto_assign:
            assign_to_users = self.th_team_id.th_user_auto_assign_ids
            if not assign_to_users:
                return
            # Lấy số lượng phiếu cho mỗi người
            user_ticket_counts = {}
            for user in assign_to_users:
                if self.th_team_id.th_assign_method == 'equal_tickets':
                    count = self.env['th.feedback.ticket'].search_count([
                        ('th_assign_id', '=', user.id)
                    ])
                else: # equal_open_tickets
                    count = self.env['th.feedback.ticket'].search_count([
                        ('th_assign_id', '=', user.id),
                        ('th_stage_id.th_ticket_stage', 'not in', ['close', 'cancel'])
                    ])
                user_ticket_counts[user.id] = count

            # Tìm người có số lượng phiếu ít nhất
            min_tickets = min(user_ticket_counts.values())
            users_with_min = [uid for uid, count in user_ticket_counts.items() 
                            if count == min_tickets]
            
            # Gán phiếu cho người có số lượng phiếu ít nhất
            if users_with_min:
                self.th_assign_id = users_with_min[0]

            # Cập nhật số lượng phiếu cho đội
            self.th_team_id.th_ticket_count += 1
            
            
    @api.onchange('th_category_id')                
    def _onchange_th_category_id(self):
        if self.th_category_id:
            self.th_category_id.th_ticket_count = self.th_category_id.th_ticket_count + 1
    
    # xử lý logic chuyển giao phiếu
    def button_add_transferring_feedback(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Phiếu chuyển phiếu feedback',
            'res_model': 'th.feedback.transfer',
            'view_mode': 'form',
            'target': 'new',
            'res_id': self.env['th.feedback.transfer'].create({
                'th_ticket_id': self.id,
                'th_reason': '',
                'th_category_id': self.th_category_id.id,
                'th_topic_id': self.th_topic_id.id,
                'th_team_id': self.th_team_id.id or False, 
                'th_transfer_date': fields.Date.today(),
            }).id,
        }

    # Hàm gom lỗi/feedback
    def th_action_merge_tickets(self):
        # Cảnh báo nếu chọn ít hơn 2 bản ghi
        if len(self) < 2:
            raise UserError("Bạn cần chọn ít nhất 2 lỗi/feedback để tiến hành gom lỗi/feedback!")

        # Xác định các trường bắt buộc phải giống nhau
        merge_fields = ['th_category_id', 'th_topic_id', 'th_partner_code', 'th_product_id',
                        'th_component_position', 'th_error_id', 'th_error_detail_id']
        reference = self[0]  # Lấy bản ghi đầu tiên làm tham chiếu

        for ticket in self:
            # Thông báo nếu người dùng chọn phiếu không phải trạng thái Phân loại để gom
            if ticket.th_ticket_stage != 'error_classify':
                raise UserError("Vui lòng chỉ gom các lỗi/feedback đang ở trạng thái Phân loại!")
            for field in merge_fields:
                # Lấy giá trị của trường từ ticket và reference
                ticket_value = getattr(ticket, field)
                reference_value = getattr(reference, field)

                # Nếu giá trị là Many2one, lấy thuộc tính id
                if hasattr(ticket_value, "id"):
                    ticket_value = ticket_value.id
                if hasattr(reference_value, "id"):
                    reference_value = reference_value.id

                # So sánh sau khi xử lý
                if ticket_value != reference_value:
                    raise UserError("Các lỗi/feedback được chọn không có đầy đủ các điều kiện giống nhau để hợp nhất!")

        # Tạo bản ghi hợp nhất với thông tin tham chiếu và nội dung đã gộp
        merged_ticket = self.create({
            'name': reference.th_error_id.name + " - " + reference.th_error_detail_id.name + " - " + str(
                len(self)) + " - " + self[-1].th_partner_id.name,
            'th_partner_id': self[-1].th_partner_id.id,
            'th_partner_ids': [(6, 0, self.mapped("th_partner_id").ids)],
            'th_stage_id': reference.th_stage_id.id,
            'th_category_id': reference.th_category_id.id,
            'th_topic_id': reference.th_topic_id.id,
            'th_partner_code': reference.th_partner_code.id,
            'th_product_id': reference.th_product_id.id,
            'th_component_position': reference.th_component_position.id,
            'th_error_id': reference.th_error_id.id,
            'th_error_detail_id': reference.th_error_detail_id.id,
        })

        # Xóa các bản ghi cũ sau khi hợp nhất thành công
        self.unlink()

        # Chuyển hướng đến form view của bản ghi mới
        # return {
        #     'type': 'ir.actions.act_window',
        #     'res_model': 'th.feedback.ticket',
        #     'view_mode': 'form',
        #     'res_id': merged_ticket.id,
        #     'target': 'current',
        # }

    # Hàm check quyền nhân viên phân loại phiếu
    @api.depends('uid_create')
    def th_compute_check_group_feedback_classifier(self):
        for rec in self:
            rec.th_check_group_feedback_classifier = self.env.user.has_group('th_feedback.group_feedback_classifier')
